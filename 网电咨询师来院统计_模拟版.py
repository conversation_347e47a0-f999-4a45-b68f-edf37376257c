#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网电咨询师来院统计 - 模拟版
完全使用模拟数据，不依赖网络请求
"""

from typing import Dict, Optional, List
import random


def get_wdzx_consultants_list_mock():
    """
    获取网电咨询师列表 - 模拟数据
    """
    return {
        "程绍婷(ZN174)": "06BF3101ABAD4F0C9F43B1DE0090D166",
        "杜中国(ZN200)": "8E4F7B2A9C3D4E5F6A7B8C9D0E1F2A3B",
        "王瑞(ZN003)": "1A2B3C4D5E6F7A8B9C0D1E2F3A4B5C6D",
        "陈文婷(ZN085)": "6994669CC24B45C8BA83B171010F81AB",
        "李梅(ZN045)": "2B3C4D5E6F7A8B9C0D1E2F3A4B5C6D7E",
        "张三(WD001)": "3C4D5E6F7A8B9C0D1E2F3A4B5C6D7E8F",
        "李四(WD002)": "4D5E6F7A8B9C0D1E2F3A4B5C6D7E8F9A",
        "王五(WD003)": "5E6F7A8B9C0D1E2F3A4B5C6D7E8F9A0B"
    }


def get_wdzx_consultation_stats_mock(
    start_date: str = "2025-08-01",
    end_date: str = "2025-08-15",
    bookempid: str = "",
    consultant_name: str = ""
) -> Optional[Dict[str, int]]:
    """
    获取网电咨询师来院统计数据 - 模拟版

    Args:
        start_date: 开始日期
        end_date: 结束日期
        bookempid: 网电咨询师ID
        consultant_name: 咨询师名称

    Returns:
        包含统计数据的字典或None
    """
    print(f"模拟请求网电咨询师数据: {start_date} 到 {end_date}, 咨询师: {consultant_name}")
    
    # 生成模拟数据
    base_performance = random.randint(50000, 150000)
    base_count = random.randint(30, 80)
    deal_count = random.randint(5, 15)
    
    stats = {
        'total_performance': float(base_performance),
        'total_count': int(base_count),
        'deal_count': int(deal_count),
        'new_first_count': int(base_count * 0.6),
        'new_second_count': int(base_count * 0.2),
        'old_count': int(base_count * 0.2)
    }
    
    print(f"模拟数据生成成功: 总人数={stats['total_count']}, 成交={stats['deal_count']}")
    return stats


def get_all_wdzx_consultants_stats(
    start_date: str = "2025-08-01",
    end_date: str = "2025-08-15",
    user_token: str = "AD5B2BE51FEB45E1840FB33E008AA554",
    user_name: str = "ZN009",
    user_id: str = "2ADB7F12A38F4D9680997C4EB0C44496"
) -> Dict:
    """
    获取所有网电咨询师的统计数据 - 模拟版

    Args:
        start_date: 开始日期
        end_date: 结束日期
        user_token: 用户令牌
        user_name: 用户名
        user_id: 用户ID

    Returns:
        dict: 包含所有网电咨询师统计数据的字典
    """
    print(f"开始获取网电咨询师统计数据（模拟版）: {start_date} 到 {end_date}")
    
    # 获取网电咨询师列表（使用模拟数据）
    consultants = get_wdzx_consultants_list_mock()
    print(f"获取到 {len(consultants)} 个网电咨询师")

    # 存储所有咨询师的数据
    all_stats = []
    total_summary = {
        'total_performance': 0,
        'total_count': 0,
        'deal_count': 0,
        'new_first_count': 0,
        'new_second_count': 0,
        'old_count': 0
    }

    # 获取每个网电咨询师的数据
    for consultant_name, consultant_id in consultants.items():
        print(f"正在获取 {consultant_name} 的数据...")
        
        stats = get_wdzx_consultation_stats_mock(
            start_date=start_date,
            end_date=end_date,
            bookempid=consultant_id,
            consultant_name=consultant_name
        )
        
        if stats:
            # 添加咨询师名称
            stats['consultant_name'] = consultant_name
            stats['consultant_id'] = consultant_id
            all_stats.append(stats)
            
            # 累加到总计
            for key in total_summary:
                total_summary[key] += stats.get(key, 0)
            
            print(f"✓ {consultant_name}: 总人数={stats['total_count']}, 成交={stats['deal_count']}")
        else:
            print(f"✗ 获取 {consultant_name} 数据失败")

    print(f"网电咨询师数据获取完成，共 {len(all_stats)} 个咨询师有数据")
    
    return {
        'summary': total_summary,
        'consultants': all_stats,
        'consultant_count': len(all_stats)
    }


if __name__ == "__main__":
    print("开始测试网电咨询师统计（模拟版）...")
    
    # 测试获取所有网电咨询师统计数据
    result = get_all_wdzx_consultants_stats(
        start_date="2025-08-15",
        end_date="2025-08-21"
    )
    
    print("\n=== 网电咨询师统计结果 ===")
    print(f"汇总数据: {result.get('summary', {})}")
    print(f"咨询师数量: {result.get('consultant_count', 0)}")
    
    if result.get('consultants'):
        print("\n各咨询师数据:")
        for consultant in result['consultants']:
            print(f"  {consultant['consultant_name']}: 总人数={consultant['total_count']}, 成交={consultant['deal_count']}")
    
    print("\n测试完成！")
