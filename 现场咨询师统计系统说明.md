# 现场咨询师来院数据统计系统

## 🎯 系统概述

这是一个基于Flask + ECharts的现场咨询师来院数据统计Web应用，提供丰富的数据可视化功能。

## 📁 文件结构

```
ZN_DATA/
├── consultant_stats_app.py      # 主Flask应用
├── test_app.py                   # 测试应用（含模拟数据）
├── run_consultant_stats.py      # 启动脚本
├── templates/
│   └── consultant_stats.html    # HTML模板
├── static/
│   └── consultant_stats.js      # JavaScript文件
└── 现场咨询来院统计.py           # 数据源文件
```

## 🚀 功能特性

### 1. 数据概览
- **汇总卡片**：显示总开单业绩、总人数、成交人数、成交率、客单价
- **日期筛选**：支持自定义开始和结束日期

### 2. 图表展示

#### 饼状图
- **客户类型占比**：老客、新客首次、新客二次的比例分布

#### 柱状图（4个）
1. **客户类型堆叠柱状图**：各咨询师的老客、新客首次、新客二次分布
2. **总开单业绩柱状图**：各咨询师的业绩对比
3. **成交率柱状图**：各咨询师的成交率对比
4. **客单价柱状图**：各咨询师的客单价对比

#### 折线图（4个）
1. **总人数趋势**：指定咨询师的总人数时间趋势
2. **客户类型趋势**：指定咨询师的客户类型时间趋势
3. **成交率趋势**：指定咨询师的成交率时间趋势
4. **客单价趋势**：指定咨询师的客单价时间趋势

## 🔧 技术栈

- **后端**：Flask (Python)
- **前端**：HTML5 + CSS3 + JavaScript
- **图表库**：ECharts 5.4.3
- **数据处理**：Pandas
- **数据源**：Excel文件（通过现场咨询来院统计.py获取）

## 📊 数据流程

1. **数据获取**：调用`现场咨询来院统计.py`的`get_all_consultants_stats_to_excel`方法
2. **数据处理**：读取Excel文件，计算汇总统计
3. **API接口**：提供RESTful API返回JSON数据
4. **前端展示**：使用ECharts渲染各种图表

## 🌐 API接口

### 1. 获取统计数据
```
GET /api/stats?start_date=2025-08-01&end_date=2025-08-18
```

**返回数据结构：**
```json
{
  "summary": {
    "total_performance": 1500000,
    "total_people": 150,
    "total_deal": 45,
    "deal_rate": 30.0,
    "avg_price": 33333
  },
  "pie_data": [
    {"name": "老客", "value": 20},
    {"name": "新客首次", "value": 80},
    {"name": "新客二次", "value": 50}
  ],
  "consultants": [...],
  "bar_data": {...}
}
```

### 2. 获取趋势数据
```
GET /api/consultant_trend?consultant_name=张三(ZN001)&start_date=2025-08-01&end_date=2025-08-18
```

## 🎨 界面设计

### 布局结构
1. **顶部标题**：现场咨询师来院数据统计
2. **日期控制**：开始日期 + 结束日期 + 查询按钮
3. **汇总卡片**：5个关键指标卡片
4. **饼状图**：客户类型占比
5. **柱状图网格**：4个柱状图并排显示
6. **咨询师选择器**：下拉选择框
7. **折线图网格**：4个趋势图并排显示

### 样式特点
- **响应式设计**：支持不同屏幕尺寸
- **现代化UI**：卡片式布局，阴影效果
- **蓝色主题**：#1890ff主色调
- **图表交互**：悬停提示，缩放功能

## 🚀 启动方式

### 方法1：使用启动脚本
```bash
python run_consultant_stats.py
```

### 方法2：直接运行主应用
```bash
python consultant_stats_app.py
```

### 方法3：测试应用（含模拟数据）
```bash
python test_app.py
```

## 🌍 访问地址

- **主应用**：http://localhost:5001
- **测试应用**：http://localhost:5002

## 📝 使用说明

1. **启动应用**：运行任一启动方式
2. **打开浏览器**：访问对应地址
3. **选择日期**：设置查询的开始和结束日期
4. **查看数据**：点击查询按钮获取统计数据
5. **查看趋势**：选择特定咨询师查看趋势图

## ⚠️ 注意事项

1. **数据依赖**：需要`现场咨询来院统计.py`文件和相关数据源
2. **端口占用**：确保5001和5002端口未被占用
3. **Excel文件**：确保有读取Excel文件的权限
4. **网络连接**：ECharts通过CDN加载，需要网络连接

## 🔧 自定义配置

### 修改端口
在应用文件中修改：
```python
app.run(debug=True, host='0.0.0.0', port=5001)  # 修改port参数
```

### 修改图表样式
在`static/consultant_stats.js`中修改ECharts配置：
```javascript
itemStyle: { color: '#1890ff' }  // 修改颜色
```

### 添加新图表
1. 在HTML中添加图表容器
2. 在JavaScript中初始化图表
3. 在后端API中提供数据

## 🎉 功能完成

✅ **已实现的功能**：
- 完整的Web界面
- 5个汇总指标卡片
- 1个饼状图（客户类型占比）
- 4个柱状图（客户分布、业绩、成交率、客单价）
- 4个折线图（趋势分析）
- 日期筛选功能
- 咨询师选择功能
- 响应式设计
- 数据API接口

现在你可以启动应用并查看完整的现场咨询师来院数据统计系统了！🎉
