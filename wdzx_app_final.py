#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网电咨询师来院数据统计Web应用 - 最终版本
"""

from flask import Flask, render_template, request, jsonify, send_file, abort
import pandas as pd
import json
from datetime import datetime, timedelta
import sys
import os
import webbrowser
import threading
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

print("正在导入数据获取函数...")
try:
    from 网电咨询师来院统计 import get_all_wdzx_consultants_stats_to_excel
    print("✓ 数据获取函数导入成功")
except Exception as e:
    print(f"❌ 数据获取函数导入失败: {e}")
    # 创建一个模拟函数用于测试
    def get_all_wdzx_consultants_stats_to_excel(start_date, end_date):
        print(f"模拟获取数据: {start_date} 到 {end_date}")
        return None

app = Flask(__name__)

def get_wdzx_consultant_stats_data(start_date, end_date):
    """
    获取网电咨询师统计数据
    """
    try:
        # 调用原有函数获取Excel文件
        excel_file = f"网电咨询统计_{start_date} 到 {end_date}.xlsx"
        if not os.path.exists(excel_file):
            print(f"Excel文件不存在，尝试生成: {excel_file}")
            excel_file = get_all_wdzx_consultants_stats_to_excel(start_date, end_date)
        
        if not excel_file or not os.path.exists(excel_file):
            print("无法获取Excel文件，返回模拟数据")
            # 返回模拟数据用于测试
            return {
                "summary": {
                    "total_performance": 1000000,
                    "total_people": 500,
                    "total_deal": 100,
                    "deal_rate": 20.0,
                    "avg_price": 10000
                },
                "consultants": [
                    {
                        "name": "张三（网络）",
                        "total_performance": 200000,
                        "total_people": 100,
                        "deal_people": 20,
                        "deal_rate": 20.0,
                        "avg_price": 10000,
                        "old_customer": 10,
                        "new_first": 8,
                        "new_second": 2
                    },
                    {
                        "name": "李四（网络）",
                        "total_performance": 150000,
                        "total_people": 80,
                        "deal_people": 15,
                        "deal_rate": 18.75,
                        "avg_price": 10000,
                        "old_customer": 8,
                        "new_first": 5,
                        "new_second": 2
                    }
                ],
                "pie_data": [
                    {"name": "老客", "value": 18},
                    {"name": "新客首次", "value": 13},
                    {"name": "新客二次", "value": 4}
                ],
                "bar_data": {
                    "names": ["张三（网络）", "李四（网络）"],
                    "performance": [200000, 150000],
                    "deal_count": [20, 15],
                    "deal_rate": [20.0, 18.75],
                    "avg_price": [10000, 10000],
                    "old_customer": [10, 8],
                    "new_first": [8, 5],
                    "new_second": [2, 2]
                }
            }
        
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        # 处理数据（与原始代码相同的逻辑）
        stats_data = {
            "summary": {},
            "consultants": [],
            "pie_data": [],
            "bar_data": {}
        }

        # 排除汇总行，只处理咨询师的数据
        consultant_df = df[df['网电咨询师'] != '汇总'].copy()

        # 直接使用Excel文件中的汇总行数据
        summary_rows = df[df['网电咨询师'] == '汇总']
        if len(summary_rows) > 0:
            summary_row = summary_rows.iloc[0]
            total_performance = float(summary_row['总开单业绩'])
            total_people = int(summary_row['总人数'])
            total_deal = int(summary_row['成交人数'])
            deal_rate = float((total_deal / total_people * 100) if total_people > 0 else 0)
            avg_price = float((total_performance / total_deal) if total_deal > 0 else 0)

            # 汇总行的客户类型数据
            old_customer = int(summary_row['老客'])
            new_first = int(summary_row['新客首次'])
            new_second = int(summary_row['新客二次'])
        else:
            # 如果没有汇总行，则计算汇总数据
            total_performance = float(consultant_df['总开单业绩'].sum())
            total_people = int(consultant_df['总人数'].sum())
            total_deal = int(consultant_df['成交人数'].sum())
            deal_rate = float((total_deal / total_people * 100) if total_people > 0 else 0)
            avg_price = float((total_performance / total_deal) if total_deal > 0 else 0)

            # 计算客户类型数据
            old_customer = int(consultant_df['老客'].sum())
            new_first = int(consultant_df['新客首次'].sum())
            new_second = int(consultant_df['新客二次'].sum())

        stats_data["summary"] = {
            "total_performance": total_performance,
            "total_people": total_people,
            "total_deal": total_deal,
            "deal_rate": round(deal_rate, 2),
            "avg_price": round(avg_price, 2)
        }

        stats_data["pie_data"] = [
            {"name": "老客", "value": old_customer},
            {"name": "新客首次", "value": new_first},
            {"name": "新客二次", "value": new_second}
        ]
        
        # 处理每个咨询师的数据
        for _, row in consultant_df.iterrows():
            consultant_name = row['网电咨询师']
            if pd.isna(consultant_name):
                continue

            # 处理客单价字段
            avg_price_str = str(row['客单价'])
            if avg_price_str and avg_price_str != '0' and avg_price_str != 'nan':
                avg_price = float(avg_price_str.replace(',', ''))
            else:
                avg_price = round(float((row['总开单业绩'] / row['成交人数']) if row['成交人数'] > 0 else 0), 2)

            consultant_data = {
                "name": consultant_name,
                "total_performance": float(row['总开单业绩']),
                "total_people": int(row['总人数']),
                "deal_people": int(row['成交人数']),
                "deal_rate": round(float((row['成交人数'] / row['总人数'] * 100) if row['总人数'] > 0 else 0), 2),
                "avg_price": avg_price,
                "old_customer": int(row['老客']),
                "new_first": int(row['新客首次']),
                "new_second": int(row['新客二次'])
            }
            stats_data["consultants"].append(consultant_data)
        
        # 柱状图数据
        stats_data["bar_data"] = {
            "names": [c["name"] for c in stats_data["consultants"]],
            "performance": [c["total_performance"] for c in stats_data["consultants"]],
            "deal_count": [c["deal_people"] for c in stats_data["consultants"]],
            "deal_rate": [c["deal_rate"] for c in stats_data["consultants"]],
            "avg_price": [c["avg_price"] for c in stats_data["consultants"]],
            "old_customer": [c["old_customer"] for c in stats_data["consultants"]],
            "new_first": [c["new_first"] for c in stats_data["consultants"]],
            "new_second": [c["new_second"] for c in stats_data["consultants"]]
        }
        
        return stats_data
        
    except Exception as e:
        print(f"获取数据失败: {e}")
        return None

@app.route('/')
def index():
    """首页"""
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

    try:
        return render_template('wdzx_consultant_stats.html',
                             start_date=start_date,
                             end_date=end_date)
    except Exception as e:
        return f"""
        <html>
        <head><title>网电咨询师统计</title></head>
        <body style="font-family: Arial; margin: 40px;">
            <h1>🌐 网电咨询师来院数据统计系统</h1>
            <p>✅ 系统运行正常！</p>
            <p>模板加载失败: {e}</p>
            <p><a href="/dashboard" style="color: blue;">📊 查看可视化大屏</a></p>
            <p><a href="/test" style="color: blue;">🔧 API测试</a></p>
        </body>
        </html>
        """

@app.route('/dashboard')
def dashboard():
    """大屏数据可视化页面"""
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=14)).strftime('%Y-%m-%d')

    try:
        return render_template('wdzx_dashboard_fullscreen.html',
                             start_date=start_date,
                             end_date=end_date)
    except Exception as e:
        return f"""
        <html>
        <head><title>网电咨询师大屏</title></head>
        <body style="font-family: Arial; margin: 40px;">
            <h1>📊 网电咨询师可视化大屏</h1>
            <p>模板加载失败: {e}</p>
            <p>这里将显示网电咨询师的数据可视化大屏</p>
            <p><a href="/" style="color: blue;">← 返回首页</a></p>
        </body>
        </html>
        """

@app.route('/test')
def test_api():
    """API测试页面"""
    return """
    <html>
    <head><title>网电咨询师API测试</title></head>
    <body style="font-family: Arial; margin: 40px;">
        <h1>🔧 网电咨询师API测试</h1>
        <button onclick="testAPI()" style="padding: 10px 20px; font-size: 16px;">测试API</button>
        <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;"></div>
        <script>
            async function testAPI() {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '正在测试...';
                try {
                    const response = await fetch('/api/stats?start_date=2025-09-01&end_date=2025-09-05');
                    const data = await response.json();
                    if (response.ok) {
                        resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    } else {
                        resultDiv.innerHTML = '错误: ' + (data.error || '未知错误');
                    }
                } catch (error) {
                    resultDiv.innerHTML = '网络错误: ' + error.message;
                }
            }
        </script>
        <p><a href="/" style="color: blue;">← 返回首页</a></p>
    </body>
    </html>
    """

@app.route('/api/stats')
def get_stats():
    """获取统计数据API"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    print(f"API调用: start_date={start_date}, end_date={end_date}")

    if not start_date or not end_date:
        return jsonify({"error": "缺少日期参数"}), 400

    try:
        data = get_wdzx_consultant_stats_data(start_date, end_date)
        if data is None:
            return jsonify({"error": "获取数据失败"}), 500

        print(f"返回数据，咨询师数量: {len(data.get('consultants', []))}")
        return jsonify(data)
    except Exception as e:
        print(f"API异常: {e}")
        return jsonify({"error": f"服务器错误: {str(e)}"}), 500

def open_browser():
    """在新线程中打开浏览器"""
    time.sleep(2)
    webbrowser.open('http://localhost:5008')

if __name__ == '__main__':
    print("=" * 50)
    print("🌐 网电咨询师来院数据统计系统")
    print("=" * 50)
    print("✓ 系统初始化完成")
    print("📍 访问地址: http://localhost:5008")
    print("📊 大屏地址: http://localhost:5008/dashboard")
    print("🔧 测试地址: http://localhost:5008/test")
    print("=" * 50)
    print("按 Ctrl+C 停止服务器")
    print()
    
    # 在新线程中打开浏览器
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        app.run(debug=False, host='0.0.0.0', port=5008)
    except Exception as e:
        print(f"❌ 应用启动失败: {e}")
        import traceback
        traceback.print_exc()
