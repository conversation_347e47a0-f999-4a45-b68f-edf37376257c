import requests
import json
from datetime import datetime,timedel<PERSON>
def get_oceanengine_city_cost_data(aadvid, promotion_id, start_time, end_time):
    """
    获取巨量引擎广告投放城市消耗数据
    
    Args:
        aadvid (str): 广告主ID
        promotion_id (str): 广告计划ID
        start_time (int): 开始时间戳(毫秒)
        end_time (int): 结束时间戳(毫秒)
    
    Returns:
        dict: 返回API响应数据
    """
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9",
        "cache-control": "no-cache",
        "content-type": "application/json; charset=UTF-8",
        "origin": "https://ad.oceanengine.com",
        "pragma": "no-cache",
        "priority": "u=1, i",
        "referer": f"https://ad.oceanengine.com/promotion/promote-manage/project/ads?aadvid={aadvid}&local_project_id={promotion_id}",
        "sec-ch-ua": "Google",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "Windows",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
        "x-csrftoken": "ps3iY_6trkVxJuStbGqdTeJB"
    }

    cookies = {
        "b-user-id": "1b8da8ec-1791-528f-45be-585feda84b41",
        "gr_user_id": "d4c9e21c-8ecc-4f21-8a05-9d74c5d2348e",
        "grwng_uid": "f29c2a4f-**************-9cdb6bb0b35e",
        "store-region": "cn-ah",
        "store-region-src": "uid",
        "s_v_web_id": "verify_m7bmudnp_SuRk8R1H_1HnR_49vd_B7PC_6l8aCnVdl6VP",
        "passport_csrf_token": "f4681992cc0531677f04bc74154ae39a",
        "passport_csrf_token_default": "f4681992cc0531677f04bc74154ae39a",
        "aefa4e5d2593305f_gr_last_sent_cs1": "1803974879207498",
        "is_hit_partitioned_cookie_canary_ss": "true",
        "is_staff_user": "false",
        "is_hit_partitioned_cookie_canary": "true",
        "sso_auth_status": "df58fb707501b61ea55ddd23ffd62bcb%2C944cff39d07399bec89cd4b187b6ed71",
        "d_ticket": "670b0db99081150b6384f8973fef999530095",
        "n_mh": "we4sdJUEQEJR2yFJVWSKhZ2TUDg4P82QmFtCQsF_ze0",
        "a7b76a7621d895df_gr_last_sent_cs1": "1827622898260995",
        "a7b76a7621d895df_gr_cs1": "1827622898260995",
        "sso_auth_status_ss": "df58fb707501b61ea55ddd23ffd62bcb%2C944cff39d07399bec89cd4b187b6ed71%2C76b518bd6f8d89ec1b0a35a4f5522785",
        "sso_uid_tt": "d098c41f281978d383432e9a8922f75f",
        "sso_uid_tt_ss": "d098c41f281978d383432e9a8922f75f",
        "toutiao_sso_user": "757b461531af4675f2c295e034c18054",
        "toutiao_sso_user_ss": "757b461531af4675f2c295e034c18054",
        "uid_tt": "8d16484a4b3c7042e95b8c9e5bd4c36d",
        "uid_tt_ss": "8d16484a4b3c7042e95b8c9e5bd4c36d",
        "sid_tt": "2ff9cf52b3169a58490fdb60b9cf9894",
        "sessionid": "2ff9cf52b3169a58490fdb60b9cf9894",
        "sessionid_ss": "2ff9cf52b3169a58490fdb60b9cf9894",
        "sid_ucp_v1": "1.0.0-KDcyY2QxZTkzYmYwOGRlY2I2NTZkYjg5MDJlYzFhYTYwNzRhOTdjM2UKGQizi_Cugs2dBRDWvPy_Bhj6CiAMOAJA8QcaAmxmIiAyZmY5Y2Y1MmIzMTY5YTU4NDkwZmRiNjBiOWNmOTg5NA",
        "ssid_ucp_v1": "1.0.0-KDcyY2QxZTkzYmYwOGRlY2I2NTZkYjg5MDJlYzFhYTYwNzRhOTdjM2UKGQizi_Cugs2dBRDWvPy_Bhj6CiAMOAJA8QcaAmxmIiAyZmY5Y2Y1MmIzMTY5YTU4NDkwZmRiNjBiOWNmOTg5NA",
        "_tea_utm_cache_2906": "undefined",
        "MONITOR_WEB_ID": "8cb800b3-92b7-4951-a990-6c54ef63c2cc",
        "aefa4e5d2593305f_gr_cs1": "1803974879207498",
        "_tea_utm_cache_1192": "undefined",
        "passport_mfa_token": "Cjd5kQ1l53htWvuLORv2ETk%2Fr%2FXIongaf9JAhZuZr0kRGj45A9a76UbJv9XxiL2dKdm%2Bj54e0rUeGkoKPAAAAAAAAAAAAABO4ZIwF7L7YH16lEmCVpjHYn5m2anx9jWPJRZFEerpcQ9f1smYD%2F1Qy8xZQDsdecmnpRDF7e4NGPax0WwgAiIBA0hbUh4%3D",
        "passport_auth_status": "94f3cf371e32630eabdc4ab83dae55c7%2C45b7ae0c18f2d51233125ef7d733eb98",
        "passport_auth_status_ss": "94f3cf371e32630eabdc4ab83dae55c7%2C45b7ae0c18f2d51233125ef7d733eb98",
        "sid_ucp_sso_v1": "1.0.0-KDc5N2JjZjJiNzgwZTZiZGQwYTUzNDEwNGYzYWEyOTc4YTJmOTJjZTAKHwizi_Cugs2dBRDWvPy_BhjQDyAMMJO8ibQGOAJA8QcaAmxmIiA3NTdiNDYxNTMxYWY0Njc1ZjJjMjk1ZTAzNGMxODA1NA",
        "ssid_ucp_sso_v1": "1.0.0-KDc5N2JjZjJiNzgwZTZiZGQwYTUzNDEwNGYzYWEyOTc4YTJmOTJjZTAKHwizi_Cugs2dBRDWvPy_BhjQDyAMMJO8ibQGOAJA8QcaAmxmIiA3NTdiNDYxNTMxYWY0Njc1ZjJjMjk1ZTAzNGMxODA1NA",
        "odin_tt": "d81da495e799a01ebe5fa1a0a6222fa93d87e4ae941d42248f6a477ea0be6d54c5f6022c36a0e698961d8edcbaef5c05bccf42d37214dc4c7aa899eab557326a",
        "sid_guard": "2ff9cf52b3169a58490fdb60b9cf9894%7C1744772694%7C5184000%7CSun%2C+15-Jun-2025+03%3A04%3A54+GMT",
        "ttwid": "1%7ChJTaYfvaIDaDkop7Tptyv1EV2Cab28wCNVAvrOxu8gs%7C1744772695%7C81c090a6fc5662bcbd5e83028994a7433e982d4270631b39caae5cef00ed4833",
        "tt_scid": "Y-mIXKj5wWJaFw6txNBVhqwhPqr-aK3dTyLVUW5unKKih35TbFTYY-hFi9fLqQJl736e",
        "csrftoken": "ps3iY_6trkVxJuStbGqdTeJB",
        "csrf_session_id": "01516454031d4b94ed64d3b35c6cfc57",
        "is_force_toggle_superior_1827007702286665": "1",
        "get_new_msg_quota": "3",
        "is_force_toggle_superior_1824478262766681": "1",
        "get_new_msg_timer_cycle": "Fri Apr 18 2025 09:11:52 GMT+0800"
    }

    url = "https://ad.oceanengine.com/report/api/statistic/customize_report/data"
    params = {
        "aadvid": aadvid
    }

    payload = {
        "dimensions": ["city_name"],
        "metrics": ["stat_cost"],
        "filters": [
            {
                "field": "stat_cost",
                "type": 3,
                "operator": 4,
                "values": ["0"]
            },
            {
                "field": "cdp_promotion_id",
                "type": 2,
                "operator": 0,
                "values": [promotion_id]
            }
        ],
        "startTime": str(start_time),
        "endTime": str(end_time),
        "orderBy": [
            {
                "field": "stat_cost",
                "type": 1
            }
        ],
        "page": {
            "offset": 0,
            "limit": 30
        },
        "dataTopic": "basic_ad_data"
    }

    try:
        response = requests.post(
            url, 
            headers=headers, 
            cookies=cookies, 
            params=params, 
            json=payload,
            timeout=30
        )
        response.raise_for_status()  # 检查响应状态
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"请求发生错误: {str(e)}")
        return None
def convert_to_timestamp(date_str, date_format="%Y-%m-%d"):
    """
    将给定的日期字符串转换为时间戳

    Args:
        date_str (str): 日期字符串
        date_format (str): 日期字符串的格式，默认为"%Y-%m-%d %H:%M:%S"

    Returns:
        int: 返回时间戳（秒）
    """
    dt = datetime.strptime(date_str, date_format)
    timestamp = int(dt.timestamp() * 1000)  # 转换为毫秒时间戳
    return timestamp
# 使用示例
if __name__ == "__main__":
    # 示例参数
    aadvid = "1824478262766681"
    promotion_id = "7491876830088495114"
    
    start_time = 1744300800000  # 开始时间戳
    end_time = 1744905599000    # 结束时间戳
    start_time = convert_to_timestamp("2025-04-01")
    end_time = convert_to_timestamp("2025-04-17")
    print(start_time,end_time)
    result = get_oceanengine_city_cost_data(aadvid, promotion_id, start_time, end_time)
    if result:
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print("获取数据失败")
