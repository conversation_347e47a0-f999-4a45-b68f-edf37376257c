#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试数据获取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 现场咨询来院统计 import get_all_consultants_stats_to_excel
import pandas as pd

def test_data_fetch():
    """测试数据获取"""
    try:
        print("开始测试数据获取...")
        
        # 测试获取数据
        excel_file = get_all_consultants_stats_to_excel("2025-08-01", "2025-08-18")
        
        if excel_file and os.path.exists(excel_file):
            print(f"✅ Excel文件生成成功: {excel_file}")
            
            # 读取Excel文件
            df = pd.read_excel(excel_file)
            print(f"✅ 数据读取成功，共 {len(df)} 行数据")
            print("数据列:", df.columns.tolist())
            print("前5行数据:")
            print(df.head())
            
            return True
        else:
            print("❌ Excel文件生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_data_fetch()
