#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试API返回的日期数据
"""

import requests
import json


def test_api_dates():
    """
    测试API返回的日期数据是否正确
    """
    print("=== 测试API返回的日期数据 ===\n")
    
    base_url = "http://localhost:5002"
    
    # 测试参数
    start_date = "2025-08-06"
    end_date = "2025-08-20"
    interval_days = 7
    
    print(f"📊 测试参数:")
    print(f"   开始日期: {start_date}")
    print(f"   结束日期: {end_date}")
    print(f"   时间间隔: {interval_days} 天")
    
    # 预期的时间段
    from datetime import datetime, timedelta
    
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d')
    
    print(f"\n📅 预期的时间段:")
    current_date = start_dt
    period_count = 0
    expected_dates = []
    
    while current_date <= end_dt:
        period_end_date = min(current_date + timedelta(days=interval_days - 1), end_dt)
        period_count += 1
        
        # 计算中点日期（这是后端应该返回的日期）
        mid_date = current_date + timedelta(days=interval_days // 2)
        expected_date = mid_date.strftime('%Y-%m-%d')
        expected_dates.append(expected_date)
        
        print(f"   时间段 {period_count}: {current_date.strftime('%Y-%m-%d')} 到 {period_end_date.strftime('%Y-%m-%d')} -> 中点: {expected_date}")
        current_date += timedelta(days=interval_days)
    
    print(f"\n🎯 预期返回的日期列表: {expected_dates}")
    
    # 调用API
    url = f"{base_url}/api/consultant_trend"
    params = {
        'start_date': start_date,
        'end_date': end_date,
        'interval_days': interval_days
    }
    
    print(f"\n🔍 调用API: {url}")
    print(f"   参数: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            actual_dates = data.get('dates', [])
            
            print(f"\n✅ API调用成功!")
            print(f"   实际返回的日期: {actual_dates}")
            print(f"   预期的日期: {expected_dates}")
            
            # 对比日期
            if actual_dates == expected_dates:
                print(f"   ✅ 日期数据完全正确!")
            else:
                print(f"   ❌ 日期数据不匹配!")
                
                for i, (actual, expected) in enumerate(zip(actual_dates, expected_dates)):
                    if actual == expected:
                        print(f"      时间段 {i+1}: ✅ {actual} = {expected}")
                    else:
                        print(f"      时间段 {i+1}: ❌ {actual} ≠ {expected}")
            
            # 显示完整的API响应
            print(f"\n📋 完整的API响应:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            return actual_dates
            
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    return None


def test_with_consultant():
    """
    测试指定咨询师的日期数据
    """
    print(f"\n" + "="*50)
    print("📊 测试指定咨询师的日期数据")
    print("="*50)
    
    base_url = "http://localhost:5002"
    consultant_name = "杜中国(ZN200)"
    
    params = {
        'consultant_name': consultant_name,
        'start_date': '2025-08-06',
        'end_date': '2025-08-20',
        'interval_days': 7
    }
    
    print(f"\n🔍 测试咨询师: {consultant_name}")
    
    try:
        response = requests.get(f"{base_url}/api/consultant_trend", params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            dates = data.get('dates', [])
            
            print(f"   ✅ 咨询师数据获取成功")
            print(f"   📅 返回的日期: {dates}")
            
            return dates
            
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    return None


if __name__ == "__main__":
    # 测试汇总数据的日期
    summary_dates = test_api_dates()
    
    # 测试咨询师数据的日期
    consultant_dates = test_with_consultant()
    
    # 对比两者的日期
    if summary_dates and consultant_dates:
        print(f"\n📊 日期对比:")
        print(f"   汇总数据日期: {summary_dates}")
        print(f"   咨询师数据日期: {consultant_dates}")
        
        if summary_dates == consultant_dates:
            print(f"   ✅ 两者日期一致")
        else:
            print(f"   ❌ 两者日期不一致")
    
    print(f"\n🎉 测试完成!")
    print("💡 如果API返回的日期正确，但前端显示错误，可能是:")
    print("   1. 浏览器缓存问题")
    print("   2. JavaScript代码中有其他地方在修改日期")
    print("   3. 图表库的配置问题")
