<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
</head>
<body>
    <h1>API测试页面</h1>
    <button onclick="testAPI()">测试API</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const response = await fetch('/api/stats?start_date=2025-08-01&end_date=2025-08-18');
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    resultDiv.innerHTML = '错误: ' + (data.error || '未知错误');
                }
            } catch (error) {
                resultDiv.innerHTML = '网络错误: ' + error.message;
            }
        }
    </script>
</body>
</html>
