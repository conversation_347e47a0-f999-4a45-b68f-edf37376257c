#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取现场咨询师列表
从内部系统获取最新的现场咨询师信息并格式化输出
"""

import requests
try:
    from bs4 import BeautifulSoup
except ImportError:
    print("需要安装BeautifulSoup4: pip install beautifulsoup4")
    BeautifulSoup = None


def get_consultants_list():
    """
    获取现场咨询师列表

    Returns:
        dict: 现场咨询师字典，格式为 {"姓名(工号)": "ID"}
    """

    # 请求URL
    url = "http://************/Reservation/ToHospital/Index"

    # 请求头
    headers = {
        "Accept": "text/html, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Origin": "http://************",
        "Pragma": "no-cache",
        "Referer": "http://************/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    # Cookie
    cookies = {
        "************80_AdminContext_UserName": "ZN009",
        "************80_AdminContext_UserId": "2ADB7F12A38F4D9680997C4EB0C44496",
        "************80_AdminContext_UserToken": "AD5B2BE51FEB45E1840FB33E008AA554"
    }

    # 请求数据
    data = {
        "DatetimeRegStart": "2025-08-18",
        "DatetimeRegEnd": "2025-08-18",
        "TempDatetimeRegStart": "",
        "TempDatetimeRegEnd": "",
        "CustName": "",
        "Phone": "",
        "CustStatus": "",
        "IsDeal": "",
        "IsBookCust": "",
        "SectionId": "",
        "Medias": "",
        "MediasMenus": "",
        "Ptype1": "",
        "Ptype2": "",
        "Ptype3": "",
        "ProductTypeName1s": "",
        "ProductTypeName2s": "",
        "ProductTypeNames": "",
        "TmpCustRegType": "",
        "TmpCustRegTypeMenus": "",
        "IsLab": "",
        "CustLabelId": "",
        "CustLabelMenus": "",
        "NoCustLabelId": "",
        "NoCustLabelMenus": "",
        "BookEmpId": "",
        "FieldConsultantId": "",
        "TempCreateBy": "",
        "PlanRecallEmp": "",
        "IsHospSecond": "",
        "Remark": "",
        "Province": "",
        "City": "",
        "Area": "",
        "QQ": "",
        "WeiXinNo": "",
        "GuestId": "",
        "CustCardno": "",
        "Sex": "",
        "TempRecommendEmpId": "",
        "AttentionRemark": "",
        "IsDealCust": "",
        "pageSize": "21",
        "pageCurrent": "1",
        "iscompay": "0",
        "CrossRelation": "",
        "EmployeeToChannel": "",
        "orderField": "",
        "orderDirection": "",
        "total": ""
    }

    try:
        # 发送POST请求
        response = requests.post(url, headers=headers, cookies=cookies, data=data, verify=False)
        response.raise_for_status()

        # 解析HTML
        html_content = response.text

        # 使用BeautifulSoup解析
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找现场咨询师的select元素
        consultant_select = soup.find('select', {'id': 'FieldConsultantId'})

        if not consultant_select:
            print("未找到现场咨询师选择框")
            return {}

        # 提取所有option元素
        options = consultant_select.find_all('option')

        consultants = {}

        for option in options:
            value = option.get('value', '').strip()
            text = option.get_text().strip()

            # 跳过空值和"请选择"选项
            if not value or not text or text == "请选择":
                continue

            consultants[text] = value

        return consultants

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return {}
    except Exception as e:
        print(f"解析失败: {e}")
        return {}


def format_consultants_dict(consultants):
    """
    格式化现场咨询师字典为Python代码格式

    Args:
        consultants (dict): 现场咨询师字典

    Returns:
        str: 格式化的Python字典代码
    """
    if not consultants:
        return "consultants = {}"

    lines = ["consultants = {"]

    # 按姓名排序
    sorted_items = sorted(consultants.items())

    for name, consultant_id in sorted_items:
        lines.append(f'    "{name}": "{consultant_id}",')

    lines.append("}")

    return "\n".join(lines)


def compare_with_existing(new_consultants, existing_consultants):
    """
    比较新获取的咨询师列表与现有列表的差异

    Args:
        new_consultants (dict): 新获取的咨询师列表
        existing_consultants (dict): 现有的咨询师列表

    Returns:
        dict: 包含新增、删除、修改的信息
    """
    result = {
        "added": {},      # 新增的咨询师
        "removed": {},    # 删除的咨询师
        "changed": {},    # ID发生变化的咨询师
        "unchanged": {}   # 未变化的咨询师
    }

    # 查找新增和变化的咨询师
    for name, new_id in new_consultants.items():
        if name not in existing_consultants:
            result["added"][name] = new_id
        elif existing_consultants[name] != new_id:
            result["changed"][name] = {
                "old_id": existing_consultants[name],
                "new_id": new_id
            }
        else:
            result["unchanged"][name] = new_id

    # 查找删除的咨询师
    for name, old_id in existing_consultants.items():
        if name not in new_consultants:
            result["removed"][name] = old_id

    return result


def main():
    """
    主函数
    """
    print("正在获取现场咨询师列表...")

    # 检查BeautifulSoup是否可用
    if BeautifulSoup is None:
        print("BeautifulSoup4未安装，请运行: pip install beautifulsoup4")
        return

    # 获取最新的咨询师列表
    consultants = get_consultants_list()

    if not consultants:
        print("获取咨询师列表失败")
        return

    print(f"成功获取 {len(consultants)} 个现场咨询师")

    # 格式化输出
    formatted_dict = format_consultants_dict(consultants)
    print("\n格式化的咨询师字典:")
    print("=" * 50)
    print(formatted_dict)
    print("=" * 50)

    # 现有的咨询师列表（从你提供的代码中）
    existing_consultants = {
        "曾梦平(ZN341)": "FCB459C4F14043DEB5A8B2F30089014A",
        "程绍婷(ZN174)": "06BF3101ABAD4F0C9F43B1DE0090D166",
        "程耀杰(ZN091)": "C15B7EA5BF6741568A7BB173009F0FED",
        "程媛(ZN161)": "64870963591344DE9379B1CC009A2DE0",
        "丁晓敏(ZN227)": "511681685E624BCC841EB2090116734F",
        "杜明熙(ZN325)": "6F319E6169094374A67BB2AF0110CA98",
        "杜中国(ZN200)": "8D88F0FF09A447BB92DFB1F600AB2E8D",
        "冯程程(ZN090)": "0BCB21C052524860A234B173009EC624",
        "傅妮(ZN089)": "96B8C71889D24E85B710B173009E3743",
        "公账现场咨询师(公账现场咨询师)": "BD60AF6E29F84BE1AE51B161012DD68B",
        "李丽(ZN098)": "5F9A9AC3EC5E4FCC982AB17F00EF3FE8",
        "李炜(ZN258)": "C262821BD2CA4F3E873FB22A00A86DF0",
        "刘梦婷(ZN157)": "DD87B4862FA440929F1FB1BF00E30055",
        "汪克纲(ZN130)": "3419512769F4441AA709B1A1011F9375",
        "王蓉杰(ZN096)": "B4831E86815B4A83A6D9B17600AD06B0",
        "王瑞(ZN003)": "B2A56003475847B0A264C46E3DC9C8FE",
        "吴宇晨(ZN331)": "1990FACE011647C09BC2B2BD00DC6BAE",
        "杨晓梅(ZN330)": "8FCC647F579B47D29444B2BD00DC48B4",
        "杨勇(ZN088)": "D74DAE84EB0F4C2EB7CBB173009D6AC2",
        "尤梦茹(ZN261)": "DF2A6DDA7F9F4CA2B57BB230012E42BE",
        "助理毕文敏(ZN247)": "0F3E67CF5F9542B08EB6B21E00F77984",
        "助理唐金荣(ZN314)": "D0F53B21FF134FCA84D1B29F00960630",
        "助理汪亚莲(ZN194)": "28655077C886400FBCD7B1F300B46B4A",
        "卓逸华(ZN004)": "134D23B5BE2442AFAB852FF7CF9C7593"
    }

    # 比较差异
    diff = compare_with_existing(consultants, existing_consultants)

    print(f"\n差异分析:")
    print(f"新增咨询师: {len(diff['added'])} 个")
    if diff['added']:
        for name, consultant_id in diff['added'].items():
            print(f"  + {name}: {consultant_id}")

    print(f"删除咨询师: {len(diff['removed'])} 个")
    if diff['removed']:
        for name, consultant_id in diff['removed'].items():
            print(f"  - {name}: {consultant_id}")

    print(f"ID变化咨询师: {len(diff['changed'])} 个")
    if diff['changed']:
        for name, change_info in diff['changed'].items():
            print(f"  ~ {name}: {change_info['old_id']} -> {change_info['new_id']}")

    print(f"未变化咨询师: {len(diff['unchanged'])} 个")


if __name__ == "__main__":
    main()