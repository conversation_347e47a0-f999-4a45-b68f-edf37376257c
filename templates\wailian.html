<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外链数据分析</title>
    <script src="{{ url_for('static', filename='echarts.min.js') }}"></script>
    <style>
        :root {
            --primary-color: #1890ff;
            --primary-dark: #096dd9;
            --text-light: #ffffff;
            --card-bg: rgba(255, 255, 255, 0.98);
        }
        
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: var(--text-light);
            min-height: 100vh;
            box-sizing: border-box;
        }
        
        .header {
            text-align: center;
            padding: 10px;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .stat-box {
            background: var(--card-bg);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            flex: 1;
            text-align: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .stat-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .stat-box h3 {
            color: var(--primary-dark);
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .stat-box p {
            font-size: 36px;
            font-weight: 800;
            margin: 5px 0 0;
            color: #ff4d4f;
            text-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
        }
        
        .date-filter {
            margin: 20px 0;
            background: var(--card-bg);
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .date-filter-form {
            display: flex;
            gap: 20px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .date-input-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .date-input-group label {
            color: var(--primary-dark);
            font-weight: 600;
        }
        
        .date-input-group input {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .filter-btn {
            padding: 8px 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-btn:hover {
            background: var(--primary-dark);
        }
        
        @media (max-width: 768px) {
            .stats {
                flex-direction: column;
            }
            
            .stat-box h3 {
                font-size: 14px;
            }
            
            .stat-box p {
                font-size: 28px;
            }
            
            .date-filter-form {
                flex-direction: column;
                align-items: stretch;
            }
            
            .date-input-group {
                flex-direction: column;
                align-items: stretch;
            }
        }
        
        .charts-row {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        
        .chart-container {
            background: var(--card-bg);
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            flex: 1;
        }
        
        .chart {
            height: 400px;
            width: 100%;
        }
        
        @media (max-width: 1200px) {
            .charts-row {
                flex-direction: column;
            }
        }
        
        .table-container {
            background: var(--card-bg);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        
        .table-container h3 {
            color: var(--primary-dark);
            margin: 0 0 15px 0;
            font-size: 18px;
            text-align: center;
        }
        
        .table-wrapper {
            overflow-x: auto;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            font-size: 14px;
        }
        
        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #e8e8e8;
            color: #333;
        }
        
        .data-table th {
            background: #f5f5f5;
            font-weight: 600;
        }
        
        .data-table tr:hover {
            background-color: #f5f5f5;
        }
        
        @media (max-width: 768px) {
            .data-table th,
            .data-table td {
                padding: 8px;
                font-size: 12px;
            }
        }
        
        .header {
            text-align: center;
            padding: 10px;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }
        
        .nav-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .nav-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
            backdrop-filter: blur(5px);
        }
        
        .nav-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .nav-btn.active {
            background-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        @media (max-width: 768px) {
            .nav-buttons {
                flex-direction: column;
                width: 100%;
            }
            
            .nav-btn {
                width: 100%;
                text-align: center;
            }
        }
        
        .float-nav {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .float-btn {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: #1890ff;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .float-btn:hover {
            transform: translateX(-5px);
            background: #1890ff;
            color: white;
            box-shadow: 0 2px 15px rgba(24, 144, 255, 0.3);
        }

        .nav-icon {
            font-style: normal;
        }

        @media (max-width: 768px) {
            .float-nav {
                right: 10px;
            }
            
            .float-btn {
                width: 40px;
                height: 40px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>外链数据分析</h1>
        <div class="nav-buttons">
            <a href="/feiyu" class="nav-btn">抖音飞鱼数据分析</a>
            <a href="/friend_circle" class="nav-btn">朋友圈数据分析</a>
            <a href="/wailian" class="nav-btn active">外链数据分析</a>
            <a href="/ks" class="nav-btn">快手数据分析</a>
        </div>
    </div>
    
    <div class="date-filter">
        <form id="dateFilterForm" class="date-filter-form">
            <div class="date-input-group">
                <label for="start_date">开始时间：</label>
                <input type="date" id="start_date" name="start_date" 
                       value="{{ start_date }}" 
                       min="{{ min_date }}" 
                       max="{{ max_date }}">
            </div>
            <div class="date-input-group">
                <label for="end_date">结束时间：</label>
                <input type="date" id="end_date" name="end_date" 
                       value="{{ end_date }}" 
                       min="{{ min_date }}" 
                       max="{{ max_date }}">
            </div>
            <button type="submit" class="filter-btn">筛选</button>
        </form>
    </div>
    
    <div class="stats">
        <div class="stat-box">
            <h3>电话总数</h3>
            <p>{{ total_phones }}</p>
        </div>
    </div>

    <div class="charts-row">
        <div class="chart-container">
            <div id="noteChart" class="chart"></div>
        </div>
        <div class="chart-container">
            <div id="recorderChart" class="chart"></div>
        </div>
    </div>

    <div class="table-container">
        <h3>建档人数据统计</h3>
        <div class="table-wrapper">
            <table class="data-table">
                <thead>
                    <tr>
                        <th>建档人</th>
                        <th>总手机号数</th>
                        {% for note in note_series %}
                        <th>{{ note.name }}</th>
                        {% endfor %}
                        <th>有效率</th>
                    </tr>
                </thead>
                <tbody>
                    {% for stat in recorder_note_stats %}
                    <tr>
                        <td>{{ stat.recorder }}</td>
                        <td>{{ stat.total }}</td>
                        {% for note in note_series %}
                        <td>{{ stat.notes.get(note.name, 0) }}</td>
                        {% endfor %}
                        <td>{{ stat.valid_rate }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <div class="float-nav">
        <a href="/ks" class="float-btn" title="快手数据分析">
            <i class="nav-icon">快手</i>
        </a>
        <a href="/" class="float-btn" title="返回首页">
            <i class="nav-icon">首页</i>
        </a>
    </div>

    <script>
        document.getElementById('dateFilterForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;
            
            // 验证日期
            if (startDate > endDate) {
                alert('开始时间不能大于结束时间');
                return;
            }
            
            // 构建URL并跳转
            const url = new URL(window.location.href);
            url.searchParams.set('start_date', startDate);
            url.searchParams.set('end_date', endDate);
            window.location.href = url.toString();
        });

        // 确保结束时间不能小于开始时间
        document.getElementById('start_date').addEventListener('change', function() {
            document.getElementById('end_date').min = this.value;
        });

        // 确保开始时间不能大于结束时间
        document.getElementById('end_date').addEventListener('change', function() {
            document.getElementById('start_date').max = this.value;
        });

        function initCharts() {
            if (typeof echarts === 'undefined') {
                console.log('等待 ECharts 加载...');
                setTimeout(initCharts, 100);
                return;
            }

            const colorPalette = ['#1890ff', '#36cfc9', '#73d13d', '#ffc53d', '#ff7a45', '#ff4d4f'];
            
            // 初始化客服备注饼图
            const noteChart = echarts.init(document.getElementById('noteChart'));
            const noteOption = {
                title: {
                    text: '客服备注分布',
                    left: 'center',
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        const total = noteChart.getOption().series[0].data.reduce((sum, item) => sum + item.value, 0);
                        const percentage = ((params.value / total) * 100).toFixed(1);
                        return `${params.name}<br/>${params.value} (${percentage}%)`;
                    }
                },
                series: [{
                    name: '客服备注',
                    type: 'pie',
                    radius: '60%',
                    data: {{ note_series | tojson | safe }},
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        show: true,
                        formatter: '{b}: {d}%'
                    }
                }]
            };
            noteChart.setOption(noteOption);

            // 初始化建档人柱状图
            const recorderChart = echarts.init(document.getElementById('recorderChart'));
            const recorderOption = {
                title: {
                    text: '建档人手机号数量统计',
                    left: 'center',
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: {{ recorder_names | tojson | safe }},
                    axisLabel: {
                        interval: 0,
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '手机号数量'
                },
                series: [{
                    data: {{ recorder_values | tojson | safe }},
                    type: 'bar',
                    barWidth: '60%',
                    itemStyle: {
                        color: '#1890ff',
                        borderRadius: [5, 5, 0, 0]
                    },
                    label: {
                        show: true,
                        position: 'top'
                    }
                }]
            };
            recorderChart.setOption(recorderOption);

            // 响应式调整
            window.addEventListener('resize', function() {
                noteChart.resize();
                recorderChart.resize();
            });
        }

        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', function() {
            // 获取当前页面路径
            const currentPath = window.location.pathname;
            
            // 获取所有导航按钮
            const navButtons = document.querySelectorAll('.nav-btn');
            
            // 根据当前路径设置活动按钮
            navButtons.forEach(btn => {
                if (btn.getAttribute('href') === currentPath) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
            
            // 其他初始化代码...
            initCharts();
        });
    </script>
</body>
</html> 