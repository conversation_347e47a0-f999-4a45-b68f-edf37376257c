#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试汇总趋势图功能（不选择咨询师）
"""

import requests
import json


def test_summary_trend():
    """
    测试汇总趋势图API（不传consultant_name参数）
    """
    print("=== 测试汇总趋势图API ===\n")
    
    base_url = "http://localhost:5002"
    
    # 测试参数
    start_date = "2025-08-06"
    end_date = "2025-08-20"
    interval_days = 7
    
    print(f"📊 测试参数:")
    print(f"   开始日期: {start_date}")
    print(f"   结束日期: {end_date}")
    print(f"   时间间隔: {interval_days} 天")
    print(f"   咨询师: 未选择（汇总数据）")
    
    # 调用API（不传consultant_name参数）
    url = f"{base_url}/api/consultant_trend"
    params = {
        'start_date': start_date,
        'end_date': end_date,
        'interval_days': interval_days
    }
    
    print(f"\n🔍 调用汇总趋势API: {url}")
    print(f"   参数: {params}")
    
    try:
        response = requests.get(url, params=params, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"\n✅ API调用成功!")
            print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 验证数据结构
            if 'dates' in data and 'total_people' in data:
                dates = data.get('dates', [])
                total_people = data.get('total_people', [])
                old_customer = data.get('old_customer', [])
                new_first = data.get('new_first', [])
                new_second = data.get('new_second', [])
                deal_rate = data.get('deal_rate', [])
                avg_price = data.get('avg_price', [])
                total_performance = data.get('total_performance', [])
                
                print(f"\n📈 汇总趋势数据:")
                print(f"   数据点数量: {len(dates)}")
                print(f"   日期列表: {dates}")
                print(f"   总人数: {total_people}")
                print(f"   老客: {old_customer}")
                print(f"   新客首次: {new_first}")
                print(f"   新客二次: {new_second}")
                print(f"   成交率: {deal_rate}")
                print(f"   客单价: {avg_price}")
                print(f"   总业绩: {total_performance}")
                
                # 验证数据合理性
                print(f"\n🔍 数据验证:")
                
                # 检查数据点数量
                expected_periods = 3  # 2025-08-06到2025-08-20，按7天分割应该有3个时间段
                if len(dates) == expected_periods:
                    print(f"   ✅ 数据点数量正确: {len(dates)} = {expected_periods}")
                else:
                    print(f"   ⚠️  数据点数量: {len(dates)}，预期: {expected_periods}")
                
                # 检查数据是否为汇总数据（应该比单个咨询师的数据大）
                total_sum = sum(total_people)
                if total_sum > 0:
                    print(f"   ✅ 汇总数据有效，总人数: {total_sum}")
                else:
                    print(f"   ⚠️  汇总数据为0，可能该时间段没有数据")
                
                # 检查各客户类型数据
                old_sum = sum(old_customer)
                new_first_sum = sum(new_first)
                new_second_sum = sum(new_second)
                
                print(f"   📊 客户类型分布:")
                print(f"      老客总数: {old_sum}")
                print(f"      新客首次总数: {new_first_sum}")
                print(f"      新客二次总数: {new_second_sum}")
                print(f"      客户类型总和: {old_sum + new_first_sum + new_second_sum}")
                
                if old_sum + new_first_sum + new_second_sum == total_sum:
                    print(f"   ✅ 客户类型数据一致性验证通过")
                else:
                    print(f"   ⚠️  客户类型数据不一致")
                
                return True
                
            else:
                print(f"❌ 响应数据格式错误，缺少必要字段")
                
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")
    
    return False


def test_specific_consultant_vs_summary():
    """
    对比特定咨询师和汇总数据
    """
    print(f"\n" + "="*50)
    print("📊 对比特定咨询师和汇总数据")
    print("="*50)
    
    base_url = "http://localhost:5002"
    params = {
        'start_date': '2025-08-06',
        'end_date': '2025-08-20',
        'interval_days': 7
    }
    
    # 1. 获取汇总数据
    print(f"\n🔍 获取汇总数据...")
    try:
        response = requests.get(f"{base_url}/api/consultant_trend", params=params, timeout=10)
        if response.status_code == 200:
            summary_data = response.json()
            summary_total = sum(summary_data.get('total_people', []))
            print(f"   ✅ 汇总数据获取成功，总人数: {summary_total}")
        else:
            print(f"   ❌ 汇总数据获取失败: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 汇总数据获取异常: {e}")
        return
    
    # 2. 获取特定咨询师数据
    consultant_name = "杜中国(ZN200)"
    print(f"\n🔍 获取咨询师数据: {consultant_name}")
    
    params_with_consultant = params.copy()
    params_with_consultant['consultant_name'] = consultant_name
    
    try:
        response = requests.get(f"{base_url}/api/consultant_trend", params=params_with_consultant, timeout=10)
        if response.status_code == 200:
            consultant_data = response.json()
            consultant_total = sum(consultant_data.get('total_people', []))
            print(f"   ✅ 咨询师数据获取成功，总人数: {consultant_total}")
            
            # 3. 对比数据
            print(f"\n📈 数据对比:")
            print(f"   汇总数据总人数: {summary_total}")
            print(f"   {consultant_name} 总人数: {consultant_total}")
            
            if summary_total >= consultant_total:
                print(f"   ✅ 数据逻辑正确：汇总数据 >= 单个咨询师数据")
            else:
                print(f"   ⚠️  数据异常：汇总数据 < 单个咨询师数据")
                
        else:
            print(f"   ❌ 咨询师数据获取失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 咨询师数据获取异常: {e}")


if __name__ == "__main__":
    # 测试汇总趋势图
    success = test_summary_trend()
    
    if success:
        # 对比测试
        test_specific_consultant_vs_summary()
    
    print(f"\n" + "="*50)
    print("🎉 测试完成!")
    print("="*50)
    print("💡 现在您可以在大屏界面上:")
    print("   1. 不选择咨询师，查看汇总趋势图")
    print("   2. 选择特定咨询师，查看个人趋势图")
    print("   3. 两种模式都会按时间段正确分割数据")
