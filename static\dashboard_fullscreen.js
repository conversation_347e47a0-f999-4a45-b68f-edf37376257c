// 全局变量
let currentData = null;
let charts = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updateDateTime();
    setInterval(updateDateTime, 1000); // 每秒更新时间
    initCharts();
    loadData();

    // 添加咨询师选择变化事件监听
    const consultantSelect = document.getElementById('consultant-select');
    if (consultantSelect) {
        consultantSelect.addEventListener('change', function() {
            console.log('咨询师选择改变:', this.value);
            loadTrendData(); // 重新加载趋势数据
        });
    }
});

// 更新日期时间显示
function updateDateTime() {
    const now = new Date();
    const dateTimeStr = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    const datetimeElement = document.getElementById('datetime');
    if (datetimeElement) {
        datetimeElement.textContent = dateTimeStr;
    }
}

// 初始化所有图表
function initCharts() {
    // 深色主题配置
    const darkTheme = {
        backgroundColor: 'transparent',
        textStyle: {
            color: '#e2e8f0'
        },
        title: {
            textStyle: {
                color: '#e2e8f0'
            }
        },
        legend: {
            textStyle: {
                color: '#e2e8f0'
            }
        },
        tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff',
            textStyle: {
                color: '#ffffff'
            }
        }
    };

    // 初始化图表，添加错误检查
    const chartElements = {
        pieChart: 'pie-chart',
        customerTypeBar: 'customer-type-bar',
        performanceBar: 'performance-bar',
        dealCountBar: 'deal-count-bar',
        dealRateBar: 'deal-rate-bar',
        avgPriceBar: 'avg-price-bar',
        performanceLine: 'performance-line',
        peopleTrend: 'people-trend',
        customerTrend: 'customer-trend',
        dealRateTrend: 'deal-rate-trend',
        avgPriceTrend: 'avg-price-trend'
    };

    for (const [chartName, elementId] of Object.entries(chartElements)) {
        const element = document.getElementById(elementId);
        if (element) {
            charts[chartName] = echarts.init(element, 'dark');
            console.log(`图表 ${chartName} 初始化成功，元素ID: ${elementId}`);
        } else {
            console.error(`找不到图表元素: ${elementId}`);
        }
    }

    console.log('所有图表初始化完成，charts对象:', Object.keys(charts));

    // 窗口大小改变时重新调整图表
    window.addEventListener('resize', function() {
        Object.values(charts).forEach(chart => chart.resize());
    });
}

// 加载数据
async function loadData() {
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;

    console.log('开始加载数据，日期:', startDate, endDate);

    if (!startDate || !endDate) {
        console.log('日期为空，显示错误');
        showError('请选择开始和结束日期');
        return;
    }

    try {
        showLoading();
        console.log('发送API请求...');
        const response = await fetch(`/api/stats?start_date=${startDate}&end_date=${endDate}`);
        console.log('API响应状态:', response.status);
        const data = await response.json();

        if (response.ok) {
            console.log('获取到数据:', data);
            console.log('汇总数据:', data.summary);
            console.log('咨询师数据:', data.consultants);
            currentData = data;

            console.log('更新汇总卡片...');
            updateSummaryCards(data.summary);
            console.log('更新饼图...');
            updatePieChart(data.pie_data);
            console.log('更新柱状图...');
            updateBarCharts(data.bar_data);
            console.log('更新咨询师选择器...');
            updateConsultantSelector(data.consultants);
            console.log('加载趋势数据...');
            loadTrendData(); // 自动加载趋势数据
        } else {
            console.log('API返回错误:', data.error);
            showError(data.error || '获取数据失败');
        }
    } catch (error) {
        console.error('Error loading data:', error);
        showError('网络错误，请稍后重试');
    }
}

// 更新汇总卡片
function updateSummaryCards(summary) {
    console.log('updateSummaryCards 被调用，数据:', summary);

    const totalPerformanceEl = document.getElementById('total-performance');
    const totalPeopleEl = document.getElementById('total-people');
    const totalDealEl = document.getElementById('total-deal');
    const dealRateEl = document.getElementById('deal-rate');
    const avgPriceEl = document.getElementById('avg-price');

    console.log('DOM元素查找结果:', {
        totalPerformanceEl: !!totalPerformanceEl,
        totalPeopleEl: !!totalPeopleEl,
        totalDealEl: !!totalDealEl,
        dealRateEl: !!dealRateEl,
        avgPriceEl: !!avgPriceEl
    });

    if (totalPerformanceEl) totalPerformanceEl.textContent = formatNumber(summary.total_performance);
    if (totalPeopleEl) totalPeopleEl.textContent = summary.total_people;
    if (totalDealEl) totalDealEl.textContent = summary.total_deal;
    if (dealRateEl) dealRateEl.textContent = summary.deal_rate + '%';
    if (avgPriceEl) avgPriceEl.textContent = formatNumber(summary.avg_price);
}

// 更新饼图 - 大屏优化版本
function updatePieChart(pieData) {
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff',
            textStyle: { color: '#ffffff' }
        },
        legend: {
            orient: 'horizontal',
            bottom: '5%',
            textStyle: {
                color: '#e2e8f0',
                fontSize: 14
            }
        },
        series: [
            {
                name: '客户类型',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['50%', '45%'],
                data: pieData.map((item, index) => ({
                    ...item,
                    itemStyle: {
                        color: ['#00d4ff', '#00ff88', '#ff6b6b'][index],
                        shadowBlur: 10,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                })),
                emphasis: {
                    itemStyle: {
                        shadowBlur: 20,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.8)'
                    }
                },
                label: {
                    fontSize: 14,
                    color: '#e2e8f0'
                }
            }
        ]
    };

    if (charts.pieChart) {
        charts.pieChart.setOption(option);
        console.log('饼图设置完成');
    } else {
        console.error('饼图未初始化');
    }
}

// 更新柱状图 - 大屏优化版本
function updateBarCharts(barData) {
    console.log('更新柱状图数据:', barData);

    // 检查数据是否有效
    if (!barData || !barData.names || barData.names.length === 0) {
        console.warn('柱状图数据为空或无效');
        return;
    }

    // 创建排序函数
    function sortDataByValue(names, values, ...otherArrays) {
        // 创建索引数组并按值排序
        const indices = Array.from({length: names.length}, (_, i) => i);
        indices.sort((a, b) => values[b] - values[a]); // 降序排列

        // 根据排序后的索引重新排列所有数组
        const sortedNames = indices.map(i => names[i]);
        const sortedValues = indices.map(i => values[i]);
        const sortedOthers = otherArrays.map(arr => indices.map(i => arr[i]));

        return [sortedNames, sortedValues, ...sortedOthers];
    }

    // 客户类型堆叠柱状图 - 按总客户数排序
    const totalCustomers = barData.old_customer.map((old, i) =>
        old + barData.new_first[i] + barData.new_second[i]
    );
    const [sortedNames1, , sortedOld, sortedNewFirst, sortedNewSecond] =
        sortDataByValue(barData.names, totalCustomers, barData.old_customer, barData.new_first, barData.new_second);

    const customerTypeOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff'
        },
        legend: {
            data: ['老客', '新客首次', '新客二次'],
            textStyle: { color: '#e2e8f0', fontSize: 12 },
            top: '5%'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '20%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: sortedNames1,
            axisLabel: {
                rotate: 45,
                color: '#a0aec0',
                fontSize: 10
            },
            axisLine: { lineStyle: { color: '#4a5568' } }
        },
        yAxis: {
            type: 'value',
            axisLabel: { color: '#a0aec0' },
            axisLine: { lineStyle: { color: '#4a5568' } },
            splitLine: { lineStyle: { color: '#2d3748' } }
        },
        series: [
            {
                name: '老客',
                type: 'bar',
                stack: '客户类型',
                data: sortedOld,
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#00d4ff' },
                        { offset: 1, color: '#0099cc' }
                    ])
                }
            },
            {
                name: '新客首次',
                type: 'bar',
                stack: '客户类型',
                data: sortedNewFirst,
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#00ff88' },
                        { offset: 1, color: '#00cc66' }
                    ])
                }
            },
            {
                name: '新客二次',
                type: 'bar',
                stack: '客户类型',
                data: sortedNewSecond,
                itemStyle: { 
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#ff6b6b' },
                        { offset: 1, color: '#cc5555' }
                    ])
                }
            }
        ]
    };

    if (charts.customerTypeBar) {
        charts.customerTypeBar.setOption(customerTypeOption);
        console.log('客户类型柱状图设置完成');
    } else {
        console.error('客户类型柱状图未初始化');
    }

    // 总开单业绩柱状图 - 按业绩排序
    const [sortedNames2, sortedPerformance] = sortDataByValue(barData.names, barData.performance);

    const performanceOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' +
                       params[0].seriesName + ': ' + formatNumber(params[0].value);
            },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: sortedNames2,
            axisLabel: {
                rotate: 45,
                color: '#a0aec0',
                fontSize: 10
            },
            axisLine: { lineStyle: { color: '#4a5568' } }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: function(value) {
                    return formatNumber(value);
                },
                color: '#a0aec0'
            },
            axisLine: { lineStyle: { color: '#4a5568' } },
            splitLine: { lineStyle: { color: '#2d3748' } }
        },
        series: [
            {
                name: '总开单业绩',
                type: 'bar',
                data: sortedPerformance,
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#ff6b6b' },
                        { offset: 1, color: '#cc5555' }
                    ]),
                    shadowBlur: 10,
                    shadowColor: 'rgba(255, 107, 107, 0.3)'
                }
            }
        ]
    };

    if (charts.performanceBar) {
        charts.performanceBar.setOption(performanceOption);
        console.log('业绩柱状图设置完成');
    } else {
        console.error('业绩柱状图未初始化');
    }

    // 成交人数柱状图 - 按成交人数排序
    const [sortedNames3, sortedDealCount] = sortDataByValue(barData.names, barData.deal_count);

    const dealCountOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' +
                       params[0].seriesName + ': ' + params[0].value + '人';
            },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: sortedNames3,
            axisLabel: {
                rotate: 45,
                color: '#a0aec0',
                fontSize: 10
            },
            axisLine: { lineStyle: { color: '#4a5568' } }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: function(value) {
                    return value + '人';
                },
                color: '#a0aec0'
            },
            axisLine: { lineStyle: { color: '#4a5568' } },
            splitLine: { lineStyle: { color: '#2d3748' } }
        },
        series: [
            {
                name: '成交人数',
                type: 'bar',
                data: sortedDealCount,
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#4ecdc4' },
                        { offset: 1, color: '#44a08d' }
                    ]),
                    shadowBlur: 10,
                    shadowColor: 'rgba(78, 205, 196, 0.3)'
                }
            }
        ]
    };

    if (charts.dealCountBar) {
        charts.dealCountBar.setOption(dealCountOption);
        console.log('成交人数柱状图设置完成');
    } else {
        console.error('成交人数柱状图未初始化');
    }

    // 成交率柱状图 - 按成交率排序
    const [sortedNames4, sortedDealRate] = sortDataByValue(barData.names, barData.deal_rate);

    const dealRateOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' +
                       params[0].seriesName + ': ' + params[0].value + '%';
            },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: sortedNames4,
            axisLabel: {
                rotate: 45,
                color: '#a0aec0',
                fontSize: 10
            },
            axisLine: { lineStyle: { color: '#4a5568' } }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: '{value}%',
                color: '#a0aec0'
            },
            axisLine: { lineStyle: { color: '#4a5568' } },
            splitLine: { lineStyle: { color: '#2d3748' } }
        },
        series: [
            {
                name: '成交率',
                type: 'bar',
                data: sortedDealRate,
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#00ff88' },
                        { offset: 1, color: '#00cc66' }
                    ]),
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 255, 136, 0.3)'
                }
            }
        ]
    };

    if (charts.dealRateBar) {
        charts.dealRateBar.setOption(dealRateOption);
        console.log('成交率柱状图设置完成');
    } else {
        console.error('成交率柱状图未初始化');
    }

    // 客单价柱状图 - 按客单价排序
    const [sortedNames5, sortedAvgPrice] = sortDataByValue(barData.names, barData.avg_price);

    const avgPriceOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' +
                       params[0].seriesName + ': ' + formatNumber(params[0].value);
            },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: sortedNames5,
            axisLabel: {
                rotate: 45,
                color: '#a0aec0',
                fontSize: 10
            },
            axisLine: { lineStyle: { color: '#4a5568' } }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: function(value) {
                    return formatNumber(value);
                },
                color: '#a0aec0'
            },
            axisLine: { lineStyle: { color: '#4a5568' } },
            splitLine: { lineStyle: { color: '#2d3748' } }
        },
        series: [
            {
                name: '客单价',
                type: 'bar',
                data: sortedAvgPrice,
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#ffd700' },
                        { offset: 1, color: '#cc9900' }
                    ]),
                    shadowBlur: 10,
                    shadowColor: 'rgba(255, 215, 0, 0.3)'
                }
            }
        ]
    };

    if (charts.avgPriceBar) {
        charts.avgPriceBar.setOption(avgPriceOption);
        console.log('客单价柱状图设置完成');
    } else {
        console.error('客单价柱状图未初始化');
    }
}

// 更新业绩折线图 - 显示选中咨询师的业绩趋势
function updatePerformanceLineChart(trendData = null) {
    console.log('updatePerformanceLineChart 被调用，数据:', trendData);

    if (!trendData || !trendData.dates || trendData.dates.length === 0) {
        console.warn('业绩折线图数据为空，显示空图表');
        // 显示空图表
        const emptyOption = {
            title: {
                text: '请选择咨询师查看业绩趋势',
                left: 'center',
                top: 'middle',
                textStyle: {
                    color: '#a0aec0',
                    fontSize: 16
                }
            },
            grid: { show: false },
            xAxis: { show: false },
            yAxis: { show: false }
        };

        if (charts.performanceLine) {
            charts.performanceLine.setOption(emptyOption);
        }
        return;
    }

    const lineOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' +
                       params[0].seriesName + ': ' + formatNumber(params[0].value);
            },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '15%',
            top: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: trendData.dates,
            axisLabel: {
                rotate: 45,
                color: '#a0aec0',
                fontSize: 10
            },
            axisLine: { lineStyle: { color: '#4a5568' } }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: function(value) {
                    return formatNumber(value);
                },
                color: '#a0aec0'
            },
            axisLine: { lineStyle: { color: '#4a5568' } },
            splitLine: { lineStyle: { color: '#2d3748' } }
        },
        series: [
            {
                name: '总开单业绩',
                type: 'line',
                data: trendData.total_performance,
                smooth: true,
                lineStyle: {
                    color: '#00d4ff',
                    width: 3,
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 212, 255, 0.3)'
                },
                itemStyle: {
                    color: '#00d4ff',
                    borderColor: '#ffffff',
                    borderWidth: 2
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(0, 212, 255, 0.3)' },
                        { offset: 1, color: 'rgba(0, 212, 255, 0.1)' }
                    ])
                },
                symbol: 'circle',
                symbolSize: 8
            }
        ]
    };

    if (charts.performanceLine) {
        charts.performanceLine.setOption(lineOption);
        console.log('业绩折线图设置完成');
    } else {
        console.error('业绩折线图未初始化');
    }
}

// 更新咨询师选择器
function updateConsultantSelector(consultants) {
    console.log('updateConsultantSelector 被调用，咨询师数量:', consultants.length);
    console.log('咨询师数据:', consultants);

    const select = document.getElementById('consultant-select');
    console.log('咨询师选择器元素:', !!select);

    if (!select) {
        console.error('找不到咨询师选择器元素');
        return;
    }

    const currentValue = select.value; // 保存当前选中的值

    select.innerHTML = '<option value="">请选择咨询师</option>';

    consultants.forEach(consultant => {
        const option = document.createElement('option');
        option.value = consultant.name;
        option.textContent = consultant.name;
        select.appendChild(option);
    });

    console.log('添加了', consultants.length, '个咨询师选项');

    // 恢复之前选中的值
    if (currentValue && consultants.some(c => c.name === currentValue)) {
        select.value = currentValue;
    }
}

// 加载趋势数据 - 支持时间间隔
async function loadTrendData() {
    const consultantName = document.getElementById('consultant-select').value;
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;
    const intervalDays = parseInt(document.getElementById('interval-days').value);

    try {
        // 构建API URL，如果没有选择咨询师，则不传consultant_name参数
        let apiUrl = `/api/consultant_trend?start_date=${startDate}&end_date=${endDate}&interval_days=${intervalDays}`;

        if (consultantName) {
            apiUrl += `&consultant_name=${encodeURIComponent(consultantName)}`;
            console.log(`加载咨询师趋势数据: ${consultantName}`);
        } else {
            console.log('加载汇总趋势数据（所有咨询师）');
        }

        const response = await fetch(apiUrl);
        const data = await response.json();

        if (response.ok) {
            console.log('✅ API调用成功，返回的趋势数据:', data);
            console.log('📅 API返回的日期数据:', data.dates);
            console.log('📊 API返回的总人数数据:', data.total_people);
            updateTrendCharts(data);
            console.log('✅ 趋势图表更新完成');
        } else {
            console.error('❌ 获取趋势数据失败:', data.error);
            console.log('🔄 回退到生成模拟数据');
            // 如果API失败，回退到生成模拟数据
            generateTrendData(startDate, endDate, intervalDays);
        }
    } catch (error) {
        console.error('Error loading trend data:', error);
        // 如果请求失败，回退到生成模拟数据
        generateTrendData(startDate, endDate, intervalDays);
    }
}

// 生成模拟趋势数据
function generateTrendData(startDate, endDate, intervalDays) {
    console.log('⚠️  正在生成模拟趋势数据 - 这不应该在API成功时发生!');
    console.log('📅 模拟数据参数:', { startDate, endDate, intervalDays });

    const start = new Date(startDate);
    const end = new Date(endDate);
    const dates = [];
    const totalPeople = [];
    const oldCustomer = [];
    const newFirst = [];
    const newSecond = [];
    const dealRate = [];
    const avgPrice = [];
    const totalPerformance = [];

    // 从昨天开始往前推算
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    let currentDate = new Date(yesterday);
    
    // 生成时间间隔数据点
    while (currentDate >= start) {
        const dateStr = currentDate.toISOString().split('T')[0];
        dates.unshift(dateStr);
        
        // 生成模拟数据
        const people = Math.floor(Math.random() * 50) + 20;
        const rate = Math.floor(Math.random() * 30) + 20;
        const price = Math.floor(Math.random() * 20000) + 25000;
        const dealCount = Math.floor(people * rate / 100);
        const performance = dealCount * price;

        totalPeople.unshift(people);
        oldCustomer.unshift(Math.floor(Math.random() * 15) + 5);
        newFirst.unshift(Math.floor(Math.random() * 25) + 10);
        newSecond.unshift(Math.floor(Math.random() * 15) + 5);
        dealRate.unshift(rate);
        avgPrice.unshift(price);
        totalPerformance.unshift(performance);
        
        // 往前推intervalDays天
        currentDate.setDate(currentDate.getDate() - intervalDays);
    }

    const trendData = {
        dates,
        total_people: totalPeople,
        old_customer: oldCustomer,
        new_first: newFirst,
        new_second: newSecond,
        deal_rate: dealRate,
        avg_price: avgPrice,
        total_performance: totalPerformance,
        isSimulated: true  // 标记这是模拟数据
    };

    console.log('🔄 生成的模拟数据:', trendData);
    console.log('📅 模拟数据的日期:', dates);
    updateTrendCharts(trendData);
}

// 更新趋势图表 - 大屏优化版本
function updateTrendCharts(trendData) {
    console.log('📊 updateTrendCharts 被调用，数据:', trendData);
    console.log('📅 日期数据:', trendData.dates);
    console.log('👥 总人数数据:', trendData.total_people);

    if (trendData.isSimulated) {
        console.log('⚠️  警告：正在使用模拟数据更新图表！');
    } else {
        console.log('✅ 正在使用真实API数据更新图表');
    }

    // 首先更新业绩折线图
    updatePerformanceLineChart(trendData);

    const commonGridConfig = {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
    };

    const commonAxisConfig = {
        axisLabel: { color: '#a0aec0', fontSize: 10 },
        axisLine: { lineStyle: { color: '#4a5568' } },
        splitLine: { lineStyle: { color: '#2d3748' } }
    };

    // 总人数趋势
    const peopleTrendOption = {
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff'
        },
        grid: commonGridConfig,
        xAxis: {
            type: 'category',
            data: trendData.dates,
            ...commonAxisConfig
        },
        yAxis: {
            type: 'value',
            ...commonAxisConfig
        },
        series: [
            {
                name: '总人数',
                type: 'line',
                data: trendData.total_people,
                smooth: true,
                lineStyle: {
                    color: '#00d4ff',
                    width: 3,
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 212, 255, 0.3)'
                },
                itemStyle: {
                    color: '#00d4ff',
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 212, 255, 0.5)'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(0, 212, 255, 0.3)' },
                        { offset: 1, color: 'rgba(0, 212, 255, 0.05)' }
                    ])
                }
            }
        ]
    };

    if (charts.peopleTrend) {
        charts.peopleTrend.setOption(peopleTrendOption);
        console.log('总人数趋势图设置完成');
    } else {
        console.error('总人数趋势图未初始化');
    }

    // 客户类型趋势
    const customerTrendOption = {
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff'
        },
        legend: {
            data: ['老客', '新客首次', '新客二次'],
            textStyle: { color: '#e2e8f0', fontSize: 10 },
            top: '5%'
        },
        grid: commonGridConfig,
        xAxis: {
            type: 'category',
            data: trendData.dates,
            ...commonAxisConfig
        },
        yAxis: {
            type: 'value',
            ...commonAxisConfig
        },
        series: [
            {
                name: '老客',
                type: 'line',
                data: trendData.old_customer,
                smooth: true,
                lineStyle: { color: '#00d4ff', width: 2 },
                itemStyle: { color: '#00d4ff' }
            },
            {
                name: '新客首次',
                type: 'line',
                data: trendData.new_first,
                smooth: true,
                lineStyle: { color: '#00ff88', width: 2 },
                itemStyle: { color: '#00ff88' }
            },
            {
                name: '新客二次',
                type: 'line',
                data: trendData.new_second,
                smooth: true,
                lineStyle: { color: '#ff6b6b', width: 2 },
                itemStyle: { color: '#ff6b6b' }
            }
        ]
    };

    if (charts.customerTrend) {
        charts.customerTrend.setOption(customerTrendOption);
        console.log('客户类型趋势图设置完成');
    } else {
        console.error('客户类型趋势图未初始化');
    }

    // 成交率趋势
    const dealRateTrendOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' +
                       params[0].seriesName + ': ' + params[0].value + '%';
            },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff'
        },
        grid: commonGridConfig,
        xAxis: {
            type: 'category',
            data: trendData.dates,
            ...commonAxisConfig
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: '{value}%',
                color: '#a0aec0',
                fontSize: 10
            },
            axisLine: { lineStyle: { color: '#4a5568' } },
            splitLine: { lineStyle: { color: '#2d3748' } }
        },
        series: [
            {
                name: '成交率',
                type: 'line',
                data: trendData.deal_rate,
                smooth: true,
                lineStyle: {
                    color: '#00ff88',
                    width: 3,
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 255, 136, 0.3)'
                },
                itemStyle: {
                    color: '#00ff88',
                    shadowBlur: 10,
                    shadowColor: 'rgba(0, 255, 136, 0.5)'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(0, 255, 136, 0.3)' },
                        { offset: 1, color: 'rgba(0, 255, 136, 0.05)' }
                    ])
                }
            }
        ]
    };

    if (charts.dealRateTrend) {
        charts.dealRateTrend.setOption(dealRateTrendOption);
        console.log('成交率趋势图设置完成');
    } else {
        console.error('成交率趋势图未初始化');
    }

    // 客单价趋势
    const avgPriceTrendOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' +
                       params[0].seriesName + ': ' + formatNumber(params[0].value);
            },
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff'
        },
        grid: commonGridConfig,
        xAxis: {
            type: 'category',
            data: trendData.dates,
            ...commonAxisConfig
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: function(value) {
                    return formatNumber(value);
                },
                color: '#a0aec0',
                fontSize: 10
            },
            axisLine: { lineStyle: { color: '#4a5568' } },
            splitLine: { lineStyle: { color: '#2d3748' } }
        },
        series: [
            {
                name: '客单价',
                type: 'line',
                data: trendData.avg_price,
                smooth: true,
                lineStyle: {
                    color: '#ff6b6b',
                    width: 3,
                    shadowBlur: 10,
                    shadowColor: 'rgba(255, 107, 107, 0.3)'
                },
                itemStyle: {
                    color: '#ff6b6b',
                    shadowBlur: 10,
                    shadowColor: 'rgba(255, 107, 107, 0.5)'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(255, 107, 107, 0.3)' },
                        { offset: 1, color: 'rgba(255, 107, 107, 0.05)' }
                    ])
                }
            }
        ]
    };

    if (charts.avgPriceTrend) {
        charts.avgPriceTrend.setOption(avgPriceTrendOption);
        console.log('客单价趋势图设置完成');
    } else {
        console.error('客单价趋势图未初始化');
    }
}

// 工具函数
function formatNumber(num) {
    if (num === null || num === undefined || isNaN(num)) return '0';
    return new Intl.NumberFormat('zh-CN').format(Math.round(num));
}

function showLoading() {
    console.log('Loading...');
}

function showError(message) {
    console.error('Error:', message);
    // 可以在这里添加更好的错误显示
}

// 报表管理功能
function showReportManager() {
    console.log('显示报表管理器');
    const modal = document.getElementById('reportModal');
    if (modal) {
        modal.classList.add('show');
        loadReportList();
    }
}

function closeReportManager() {
    console.log('关闭报表管理器');
    const modal = document.getElementById('reportModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

// 加载报表列表
async function loadReportList() {
    console.log('加载报表列表');
    const reportList = document.getElementById('reportList');

    try {
        reportList.innerHTML = '<div class="loading">正在加载报表列表...</div>';

        const response = await fetch('/api/reports');
        const data = await response.json();

        if (data.success) {
            displayReportList(data.files);

            // 更新标题显示文件数量
            const headerTitle = document.querySelector('#reportModal .report-list-header h4');
            if (headerTitle) {
                headerTitle.textContent = `现有报表文件 (${data.count}个)`;
            }
        } else {
            reportList.innerHTML = '<div class="no-reports">加载报表列表失败: ' + data.error + '</div>';
        }
    } catch (error) {
        console.error('加载报表列表失败:', error);
        reportList.innerHTML = '<div class="no-reports">网络连接失败，请检查网络后重试</div>';
    }
}

// 显示报表列表
function displayReportList(files) {
    const reportList = document.getElementById('reportList');

    if (files.length === 0) {
        reportList.innerHTML = '<div class="no-reports">暂无报表文件</div>';
        return;
    }

    let html = '';
    files.forEach((file, index) => {
        // 格式化文件名显示
        const displayName = file.filename.replace('现场咨询统计_', '').replace('.xlsx', '');

        html += `
            <div class="report-item">
                <div class="report-info">
                    <div class="report-name">${displayName}</div>
                    <div class="report-details">
                        <div class="detail-item">${file.size_mb} MB</div>
                        <div class="detail-item">${file.modified_time}</div>
                    </div>
                </div>
                <div class="report-actions">
                    <button class="download-btn" onclick="downloadReport('${file.filename}')" title="下载报表">
                        📥 下载
                    </button>
                    <button class="delete-btn" onclick="deleteReport('${file.filename}')" title="删除报表">
                        🗑️ 删除
                    </button>
                </div>
            </div>
        `;
    });

    reportList.innerHTML = html;
}

// 刷新报表列表
function refreshReportList() {
    console.log('刷新报表列表');
    loadReportList();
}

// 下载报表
function downloadReport(filename) {
    console.log('下载报表:', filename);

    // 显示下载提示
    const displayName = filename.replace('现场咨询统计_', '').replace('.xlsx', '');

    // 创建下载链接
    const link = document.createElement('a');
    link.href = `/api/reports/download/${encodeURIComponent(filename)}`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 简单的下载提示（可以用更好的通知组件替代）
    console.log(`开始下载: ${displayName}`);
}

// 删除报表
async function deleteReport(filename) {
    console.log('删除报表:', filename);

    const displayName = filename.replace('现场咨询统计_', '').replace('.xlsx', '');

    if (!confirm(`⚠️ 确定要删除报表文件吗？\n\n📄 文件名: ${displayName}\n\n❌ 此操作不可恢复！`)) {
        return;
    }

    try {
        // 显示删除中状态
        const reportList = document.getElementById('reportList');
        const originalHTML = reportList.innerHTML;
        reportList.innerHTML = '<div class="loading">正在删除报表文件...</div>';

        const response = await fetch(`/api/reports/delete/${encodeURIComponent(filename)}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            // 成功提示
            alert('✅ 报表删除成功！');
            loadReportList(); // 重新加载列表
        } else {
            // 恢复原始内容并显示错误
            reportList.innerHTML = originalHTML;
            alert('❌ 删除失败: ' + data.error);
        }
    } catch (error) {
        console.error('删除报表失败:', error);
        // 恢复原始内容并显示错误
        loadReportList();
        alert('❌ 网络错误，删除失败: ' + error.message);
    }
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const modal = document.getElementById('reportModal');
    if (event.target === modal && modal.classList.contains('show')) {
        closeReportManager();
    }
}
