<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现场咨询师来院数据统计大屏</title>
    <script src="{{ url_for('static', filename='echarts.min.js') }}"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #0c1e3f 0%, #1a365d 50%, #2d3748 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        .dashboard {
            width: 100vw;
            min-height: 100vh;
            display: grid;
            grid-template-rows: 80px 120px 1fr;
            gap: 10px;
            padding: 10px;
        }

        /* 标题区域 */
        .header {
            display: flex;
            justify-content: center;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 0 30px;
            backdrop-filter: blur(10px);
            position: relative;
        }

        .title {
            font-size: 36px;
            font-weight: bold;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
            text-align: center;
        }

        .datetime {
            position: absolute;
            right: 30px;
            font-size: 18px;
            color: #a0aec0;
        }



        /* 控制面板 */
        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            font-size: 16px;
            color: #e2e8f0;
            font-weight: bold;
        }

        .control-group input, .control-group select {
            padding: 8px 15px;
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            font-size: 14px;
            backdrop-filter: blur(5px);
        }

        .control-group input:focus, .control-group select:focus {
            outline: none;
            border-color: #00d4ff;
            box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        /* 修复下拉框选项样式 */
        .control-group select option {
            background: #2d3748;
            color: #ffffff;
            padding: 8px;
        }

        .control-group select option:hover {
            background: #4a5568;
        }

        .query-btn {
            padding: 10px 25px;
            background: linear-gradient(45deg, #00d4ff, #00ff88);
            color: #0c1e3f;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .query-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }

        /* 主内容区域 */
        .main-content {
            display: grid;
            grid-template-columns: 1fr;
            grid-template-rows: 200px auto auto auto auto;
            gap: 15px;
        }

        /* 汇总卡片区域 */
        .summary-section {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 15px;
        }

        /* 饼图区域 */
        .pie-section {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .pie-section .chart-container {
            width: 500px;
            height: 400px;
        }

        /* 柱状图区域 */
        .bar-section {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .bar-section .chart-container {
            height: 350px;
        }

        /* 折线图区域 */
        .line-section {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .line-section .chart-container {
            width: 100%;
            height: 400px;
        }

        /* 趋势图表区域 */
        .trend-section {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .trend-section .chart-container {
            height: 350px;
        }

        .summary-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00d4ff, #00ff88);
        }

        .summary-card .label {
            font-size: 14px;
            color: #a0aec0;
            margin-bottom: 10px;
        }

        .summary-card .value {
            font-size: 28px;
            font-weight: bold;
            color: #00d4ff;
            text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
        }

        /* 图表容器 */
        .chart-container {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #00d4ff, #00ff88);
        }

        .chart-title {
            font-size: 18px;
            font-weight: bold;
            color: #e2e8f0;
            margin-bottom: 15px;
            text-align: center;
        }

        .chart {
            width: 100%;
            flex: 1;
            min-height: 300px;
        }



        /* 加载和错误状态 */
        .loading, .error {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            font-size: 18px;
            color: #a0aec0;
        }

        .loading::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #00d4ff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 1600px) {
            .title { font-size: 28px; }
            .summary-card .value { font-size: 24px; }
        }

        @media (max-width: 1200px) {
            .main-content {
                grid-template-columns: 1fr;
                grid-template-rows: 200px 1fr 1fr;
            }
            
            .trend-section {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 报表管理模态框样式 - 确保最高优先级 */
        #reportModal {
            position: fixed !important;
            z-index: 99999 !important;
            left: 0 !important;
            top: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0, 0, 0, 0.8) !important;
            display: none !important;
            justify-content: center !important;
            align-items: center !important;
        }

        #reportModal.show {
            display: flex !important;
        }

        #reportModal .modal-content {
            background-color: #fff !important;
            border-radius: 10px !important;
            width: 90% !important;
            max-width: 800px !important;
            max-height: 80% !important;
            overflow: hidden !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5) !important;
            color: #333 !important;
        }

        #reportModal .modal-header {
            background-color: #007bff !important;
            color: white !important;
            padding: 15px 20px !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
        }

        #reportModal .modal-header h3 {
            margin: 0 !important;
            font-size: 18px !important;
            color: white !important;
        }

        #reportModal .close {
            font-size: 24px !important;
            font-weight: bold !important;
            cursor: pointer !important;
            line-height: 1 !important;
            color: white !important;
        }

        #reportModal .close:hover {
            opacity: 0.7 !important;
        }

        #reportModal .modal-body {
            padding: 20px !important;
            max-height: 500px !important;
            overflow-y: auto !important;
            color: #333 !important;
        }

        /* 报表列表美化样式 */
        #reportModal .report-list-header {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            margin-bottom: 20px !important;
            padding-bottom: 15px !important;
            border-bottom: 2px solid #e9ecef !important;
        }

        #reportModal .report-list-header h4 {
            margin: 0 !important;
            color: #495057 !important;
            font-size: 18px !important;
            font-weight: 600 !important;
        }

        #reportModal .refresh-btn {
            background: linear-gradient(45deg, #17a2b8, #20c997) !important;
            color: white !important;
            border: none !important;
            padding: 8px 16px !important;
            border-radius: 20px !important;
            cursor: pointer !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3) !important;
        }

        #reportModal .refresh-btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4) !important;
        }

        #reportModal .report-item {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            padding: 16px 20px !important;
            margin-bottom: 12px !important;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
            border: 1px solid #e9ecef !important;
            border-radius: 12px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
            transition: all 0.3s ease !important;
        }

        #reportModal .report-item:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12) !important;
            border-color: #007bff !important;
        }

        #reportModal .report-info {
            flex: 1 !important;
            margin-right: 15px !important;
        }

        #reportModal .report-name {
            font-weight: 600 !important;
            color: #2c3e50 !important;
            margin-bottom: 6px !important;
            font-size: 15px !important;
            line-height: 1.4 !important;
        }

        #reportModal .report-details {
            font-size: 13px !important;
            color: #6c757d !important;
            display: flex !important;
            gap: 15px !important;
            align-items: center !important;
        }

        #reportModal .report-details .detail-item {
            display: flex !important;
            align-items: center !important;
            gap: 4px !important;
        }

        #reportModal .report-details .detail-item::before {
            content: "📊" !important;
            font-size: 12px !important;
        }

        #reportModal .report-details .detail-item:nth-child(2)::before {
            content: "🕒" !important;
        }

        #reportModal .report-actions {
            display: flex !important;
            gap: 8px !important;
            flex-shrink: 0 !important;
        }

        #reportModal .download-btn {
            background: linear-gradient(45deg, #28a745, #20c997) !important;
            color: white !important;
            border: none !important;
            padding: 8px 16px !important;
            border-radius: 20px !important;
            cursor: pointer !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3) !important;
        }

        #reportModal .download-btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4) !important;
        }

        #reportModal .delete-btn {
            background: linear-gradient(45deg, #dc3545, #e74c3c) !important;
            color: white !important;
            border: none !important;
            padding: 8px 16px !important;
            border-radius: 20px !important;
            cursor: pointer !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3) !important;
        }

        #reportModal .delete-btn:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4) !important;
        }

        #reportModal .loading {
            text-align: center !important;
            padding: 40px 20px !important;
            color: #6c757d !important;
            font-size: 16px !important;
        }

        #reportModal .loading::before {
            content: "⏳" !important;
            font-size: 24px !important;
            display: block !important;
            margin-bottom: 10px !important;
        }

        #reportModal .no-reports {
            text-align: center !important;
            padding: 40px 20px !important;
            color: #6c757d !important;
            font-size: 16px !important;
        }

        #reportModal .no-reports::before {
            content: "📄" !important;
            font-size: 48px !important;
            display: block !important;
            margin-bottom: 15px !important;
            opacity: 0.5 !important;
        }

        /* 滚动条美化 */
        #reportModal .modal-body::-webkit-scrollbar {
            width: 6px !important;
        }

        #reportModal .modal-body::-webkit-scrollbar-track {
            background: #f1f1f1 !important;
            border-radius: 3px !important;
        }

        #reportModal .modal-body::-webkit-scrollbar-thumb {
            background: linear-gradient(45deg, #007bff, #0056b3) !important;
            border-radius: 3px !important;
        }

        #reportModal .modal-body::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(45deg, #0056b3, #004085) !important;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <!-- 标题区域 -->
        <div class="header">
            <h1 class="title">现场咨询师来院数据统计大屏</h1>
            <div class="datetime" id="datetime"></div>
        </div>

        <!-- 控制面板 -->
        <div class="controls">
            <div class="control-group">
                <label for="start-date">开始日期:</label>
                <input type="date" id="start-date" value="{{ start_date }}">
            </div>
            <div class="control-group">
                <label for="end-date">结束日期:</label>
                <input type="date" id="end-date" value="{{ end_date }}">
            </div>
            <div class="control-group">
                <label for="interval-days">时间间隔(天):</label>
                <input type="number" id="interval-days" value="7" min="1" max="365" placeholder="输入天数">
            </div>
            <div class="control-group">
                <label for="consultant-select">咨询师:</label>
                <select id="consultant-select">
                    <option value="">请选择咨询师</option>
                </select>
            </div>
            <button class="query-btn" onclick="loadData()">查询数据</button>
            <button class="query-btn" onclick="showReportManager()" style="background-color: #28a745; margin-left: 10px;">报表管理</button>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 汇总卡片 -->
            <div class="summary-section">
                <div class="summary-card">
                    <div class="label">总开单业绩</div>
                    <div class="value" id="total-performance">-</div>
                </div>
                <div class="summary-card">
                    <div class="label">总人数</div>
                    <div class="value" id="total-people">-</div>
                </div>
                <div class="summary-card">
                    <div class="label">成交人数</div>
                    <div class="value" id="total-deal">-</div>
                </div>
                <div class="summary-card">
                    <div class="label">成交率(%)</div>
                    <div class="value" id="deal-rate">-</div>
                </div>
                <div class="summary-card">
                    <div class="label">客单价</div>
                    <div class="value" id="avg-price">-</div>
                </div>
            </div>

            <!-- 饼图区域 -->
            <div class="pie-section">
                <div class="chart-container">
                    <div class="chart-title">客户类型占比</div>
                    <div id="pie-chart" class="chart"></div>
                </div>
            </div>

            <!-- 柱状图区域 -->
            <div class="bar-section">
                <!-- 客户类型堆叠柱状图 -->
                <div class="chart-container">
                    <div class="chart-title">咨询师客户类型分布</div>
                    <div id="customer-type-bar" class="chart"></div>
                </div>

                <!-- 总开单业绩柱状图 -->
                <div class="chart-container">
                    <div class="chart-title">咨询师总开单业绩</div>
                    <div id="performance-bar" class="chart"></div>
                </div>

                <!-- 成交率柱状图 -->
                <div class="chart-container">
                    <div class="chart-title">咨询师成交率对比</div>
                    <div id="deal-rate-bar" class="chart"></div>
                </div>

                <!-- 客单价柱状图 -->
                <div class="chart-container">
                    <div class="chart-title">咨询师客单价对比</div>
                    <div id="avg-price-bar" class="chart"></div>
                </div>
            </div>

            <!-- 成交人数柱状图区域 - 单独一行 -->
            <div class="deal-count-section">
                <div class="chart-container">
                    <div class="chart-title">咨询师成交人数对比</div>
                    <div id="deal-count-bar" class="chart"></div>
                </div>
            </div>

            <!-- 业绩折线图区域 -->
            <div class="line-section">
                <div class="chart-container">
                    <div class="chart-title">咨询师总开单业绩趋势</div>
                    <div id="performance-line" class="chart"></div>
                </div>
            </div>

            <!-- 趋势图表区域 -->
            <div class="trend-section">
                <div class="chart-container">
                    <div class="chart-title">总人数趋势</div>
                    <div id="people-trend" class="chart"></div>
                </div>
                <div class="chart-container">
                    <div class="chart-title">客户类型趋势</div>
                    <div id="customer-trend" class="chart"></div>
                </div>
                <div class="chart-container">
                    <div class="chart-title">成交率趋势</div>
                    <div id="deal-rate-trend" class="chart"></div>
                </div>
                <div class="chart-container">
                    <div class="chart-title">客单价趋势</div>
                    <div id="avg-price-trend" class="chart"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 报表管理模态框 -->
    <div id="reportModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>报表管理</h3>
                <span class="close" onclick="closeReportManager()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="report-list-container">
                    <div class="report-list-header">
                        <h4>现有报表文件</h4>
                        <button onclick="refreshReportList()" class="refresh-btn">刷新列表</button>
                    </div>
                    <div id="reportList" class="report-list">
                        <div class="loading">正在加载报表列表...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='dashboard_fullscreen.js') }}?v=20250821-2"></script>
</body>
</html>
