import requests
import json
import pandas as pd
from datetime import datetime,timedelta
import time
from pprint import pprint
def get_promotion_data(start_date, end_date):
    """
    获取巨量引擎广告数据
    
    参数:
    start_date (str): 开始日期，格式为 'YYYY-MM-DD'
    end_date (str): 结束日期，格式为 'YYYY-MM-DD'
    
    返回:
    pandas.DataFrame: 包含广告数据的DataFrame
    """
    # 将日期转换为时间戳
    print("start_date, end_date",start_date, end_date)
    # 将开始日期转换为时间戳
    start_timestamp = int(time.mktime(datetime.strptime(start_date, '%Y-%m-%d').timetuple()))
    end_timestamp = int(time.mktime((datetime.strptime(end_date, '%Y-%m-%d')+timedelta(days=1)).timetuple()))
    print(start_timestamp, end_timestamp)

    url = "https://business.oceanengine.com/nbs/api/bm/promotion/ad/get_bidding_promotion_list"
    
    headers = {
        "sec-ch-ua-platform": "Windows",
        "x-csrftoken": "iqyKE9C6YRt3raqo0F_ThoAo",
        "x-csrf-token;": "",
        "Referer": "https://business.oceanengine.com/site/account-manage/ad/bidding/superior/promotion",
        "sec-ch-ua": "Google",
        "sec-ch-ua-mobile": "?0",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Content-Type": "application/json"
    }
    
    cookies = {
        "b-user-id": "72091e51-2d21-3c6a-3978-fab56e3029a1",
        "ttcid": "a4037d82677d42d782bddc5a2abfe70217",
        "gr_user_id": "d4c9e21c-8ecc-4f21-8a05-9d74c5d2348e",
        "grwng_uid": "f29c2a4f-**************-9cdb6bb0b35e",
        "store-region": "cn-ah",
        "store-region-src": "uid",
        "is_hit_partitioned_cookie_canary_ss": "true",
        "is_staff_user": "false",
        "is_hit_partitioned_cookie_canary": "true",
        "n_mh": "we4sdJUEQEJR2yFJVWSKhZ2TUDg4P82QmFtCQsF_ze0",
        "_tea_utm_cache_481911": "{\"utm_source\":\"campaign\",\"utm_medium\":\"59620\",\"utm_campaign\":\"oe-website\",\"utm_term\":\"AD-popup\"}",
        "_tea_utm_cache_1192": "{\"utm_source\":\"campaign\",\"utm_medium\":\"59620\",\"utm_campaign\":\"oe-website\",\"utm_term\":\"AD-popup\"}",
        "_tea_utm_cache_1574": "{\"utm_source\":\"campaign\",\"utm_medium\":\"59620\",\"utm_campaign\":\"oe-website\",\"utm_term\":\"AD-popup\"}",
        "tt_scid": "EsuDluvolF.lEn32DlEfDDQ7AGMARwRoAKZUfXZzxwYxlAUQqo7Ba.F8IVwHZOGO98b1",
        "a7b76a7621d895df_gr_last_sent_cs1": "1824478262766681",
        "a7b76a7621d895df_gr_cs1": "1824478262766681",
        "s_v_web_id": "verify_mbk457t2_05EFNfoB_RXPz_4kf5_99aW_BO15ySCsbcJS",
        "aefa4e5d2593305f_gr_last_sent_cs1": "1824478263407819",
        "aefa4e5d2593305f_gr_cs1": "1824478263407819",
        "sso_auth_status_ss": "64a5dae6f28d41825d09dd4401408b1a",
        "csrftoken": "iqyKE9C6YRt3raqo0F_ThoAo",
        "csrf_session_id": "01516454031d4b94ed64d3b35c6cfc57",
        "passport_csrf_token": "cdf96cd64b0960a8c55788e19295e12f",
        "passport_csrf_token_default": "cdf96cd64b0960a8c55788e19295e12f",
        "ttwid": "1%7ChJTaYfvaIDaDkop7Tptyv1EV2Cab28wCNVAvrOxu8gs%7C1751596343%7Cf36535455eeedffaf60ed9b946cc55a8746994a66d89f5035858fe76ac00b6b1",
        "passport_mfa_token": "CjeeDV7gHtYeXEq50uxjUNE%2FZCXYmuGkL3T6Xurov4sHkbhurORjG5PXBvpqZo6gz8poK64kRO%2FvGkoKPAAAAAAAAAAAAABPMQE55VQEULQtuKzM2M1EshQBaL8%2FJ2pmgXQg5CaXfGT2O8EIA8crJkb6qb2OjTmhshCz6PUNGPax0WwgAiIBA9LvtbY%3D",
        "d_ticket": "9ed830468ab37cea9d04fd0626a7057430095",
        "sso_uid_tt": "2ce26173525f16c27850a8a16cb70125",
        "sso_uid_tt_ss": "2ce26173525f16c27850a8a16cb70125",
        "toutiao_sso_user": "1b94430137b7c38c79bd5ac1b2a785a7",
        "toutiao_sso_user_ss": "1b94430137b7c38c79bd5ac1b2a785a7",
        "sid_ucp_sso_v1": "1.0.0-KGYyMzE5YjYwMGUyNmMyZjNlZjIwOTU0ZGQ3MTc3MzcxM2IyZjdmMjYKHwizi_Cugs2dBRDo-pzDBhj6CiAMMJO8ibQGOAJA8QcaAmxmIiAxYjk0NDMwMTM3YjdjMzhjNzliZDVhYzFiMmE3ODVhNw",
        "ssid_ucp_sso_v1": "1.0.0-KGYyMzE5YjYwMGUyNmMyZjNlZjIwOTU0ZGQ3MTc3MzcxM2IyZjdmMjYKHwizi_Cugs2dBRDo-pzDBhj6CiAMMJO8ibQGOAJA8QcaAmxmIiAxYjk0NDMwMTM3YjdjMzhjNzliZDVhYzFiMmE3ODVhNw",
        "odin_tt": "eaaffbf213331d0dda5d0e28adc047369c831685b52ccc8d384753562dab686d284fe550280f842c4f5a850ddbeec7dd8a60d5c3d5a2fb5979672c3aa2095935",
        "passport_auth_status": "ff41bf6e044d1e2e05cc4554f3b404c4%2Cf745d773fa5277f7be6727e8ae33987a",
        "passport_auth_status_ss": "ff41bf6e044d1e2e05cc4554f3b404c4%2Cf745d773fa5277f7be6727e8ae33987a",
        "sid_guard": "1b9a22c0421bd13ec0b5944520a14432%7C1751596392%7C5184002%7CTue%2C+02-Sep-2025+02%3A33%3A14+GMT",
        "uid_tt": "d9e661fca3791b4e4749c271282c6b8d",
        "uid_tt_ss": "d9e661fca3791b4e4749c271282c6b8d",
        "sid_tt": "1b9a22c0421bd13ec0b5944520a14432",
        "sessionid": "1b9a22c0421bd13ec0b5944520a14432",
        "sessionid_ss": "1b9a22c0421bd13ec0b5944520a14432",
        "sid_ucp_v1": "1.0.0-KDMwZjhkNjEyZmQzODFmMzRhZWQ1NjQ4ODk1NjBiYWY1OTFhN2E2ZmIKGQizi_Cugs2dBRDo-pzDBhj6CiAMOAJA8QcaAmxmIiAxYjlhMjJjMDQyMWJkMTNlYzBiNTk0NDUyMGExNDQzMg",
        "ssid_ucp_v1": "1.0.0-KDMwZjhkNjEyZmQzODFmMzRhZWQ1NjQ4ODk1NjBiYWY1OTFhN2E2ZmIKGQizi_Cugs2dBRDo-pzDBhj6CiAMOAJA8QcaAmxmIiAxYjlhMjJjMDQyMWJkMTNlYzBiNTk0NDUyMGExNDQzMg"
    }
    
    
    all_processed_data = []  # 新增：存储所有页数据
    total_pages = 3  # 需要获取的页数
    
    try:
        for page in range(total_pages):
            payload = {
                "start_time": str(start_timestamp),
                "end_time": str(end_timestamp),
                "cascade_metrics": [
                    "promotion_name", "promotion_id", "promotion_status_first", "promotion_status_second",
                    "advertiser_name", "advertiser_id", "promotion_status_first_name", "promotion_status_second",
                    "promotion_status_second_name", "promotion_status_first_name", "promotion_status_second",
                    "promotion_status_second_name"
                ],
                "fields": [
                    "show_cnt", "cpm_platform", "stat_cost", "click_cnt", "ctr", "cpc_platform", 
                    "convert_cnt", "conversion_cost"
                ],
                "order_field": "stat_cost",
                "order_type": 1,
                "offset": page+1,  # 修改：动态计算offset
                "limit": 100,
                "account_type": 0,
                "filter": {
                    "advertiser": {},
                    "group": {},
                    "project": {"projectStatusFirst": [-1]},
                    "promotion": {"promotionStatusFirst": [-1]}
                },
                "ocean_white": True
            }
            
            response = requests.post(url, headers=headers, cookies=cookies, json=payload)
            response.raise_for_status()
            
            data = response.json()
            with open('promotion_data.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            promotion_list = data.get('data', {}).get('data_list', [])
            
            # 创建一个空列表来存储处理后的数据
            processed_data = []
            
            for item in promotion_list:
                # 处理数据，移除逗号并转换为数值类型
                show_cnt = item.get('show_cnt', '0').replace(',', '')
                click_cnt = item.get('click_cnt', '0').replace(',', '')
                stat_cost = item.get('stat_cost', '0').replace(',', '')
                convert_cnt = item.get('convert_cnt', '0').replace(',', '')
                ctr = item.get('ctr', '0%').replace('%', '')
                print(show_cnt, click_cnt, stat_cost, convert_cnt, ctr)
                # 创建一个字典来存储处理后的数据
                processed_item = {
                    '计划名称': item.get('promotion_name', ''),
                    '广告id': item.get('promotion_id', ''),
                    '广告一级状态': item.get('promotion_status_first_name', ''),
                    '广告二级状态': ', '.join(item.get('promotion_status_second_name', [])),
                    '账户': item.get('advertiser_name', ''),
                    '展示数': int(show_cnt) if show_cnt.isdigit() else 0,
                    '平均千次展示费用': float(item.get('cpm_platform', '0')),
                    '消耗': float(stat_cost),
                    '点击数': int(click_cnt) if click_cnt.isdigit() else 0,
                    '点击率(%)': float(ctr),
                    '平均点击单价': float(item.get('cpc_platform', '0')),
                    '转化数': int(convert_cnt) if convert_cnt.isdigit() else 0,
                    '转化成本': float(item.get('conversion_cost', '0'))
                }
                
                processed_data.append(processed_item)
            
            all_processed_data.extend(processed_data)  # 合并数据
            print(f"已获取第 {page+1} 页数据，共 {len(promotion_list)} 条记录")
            
            time.sleep(1)  # 新增：添加请求间隔防止被封

        # 创建最终的DataFrame
        df = pd.DataFrame(all_processed_data)
        return df
        
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return pd.DataFrame()
    except json.JSONDecodeError:
        print(f"JSON解析失败，响应内容: {response.text}")
        return pd.DataFrame()
    except Exception as e:
        print(f"处理数据时出错: {e}")
        return pd.DataFrame()

# 示例使用
if __name__ == "__main__":
    # 获取2025年3月20日到2025年3月24日的数据
    df = get_promotion_data('2025-06-01', '2025-06-16')
    
    # 打印DataFrame
    print(df)
    
    # 保存为Excel文件
    df.to_excel('promotion_data.xlsx', index=False)
    print("数据已保存到 promotion_data.xlsx")
