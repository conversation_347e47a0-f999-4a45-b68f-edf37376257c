import requests
import json
import pandas as pd
from typing import Dict, Optional, List


def get_consultation_stats(
    start_date: str = "2025-08-01",
    end_date: str = "2025-08-15",
    user_token: str = "A2E7C2EF444D4061A342B33D00A46370",
    user_name: str = "ZN009",
    user_id: str = "2ADB7F12A38F4D9680997C4EB0C44496",
    field_consultant_id: str = "",
    bookempid: str = "",
    tmp_cust_reg_type: str = "2DDBC50B8ADB488194F8D7D6180C92BD,687834CB338F47489DD6B1A301267A2D,1C223B8C2C444D4CB1C3B1A30126EF31,B919A7E2D56945BCAD1FB1A30126D76A,AC6912B932D845778B3CB2C400F7C2C4,3742881838A145318353B1A30126B8E4,68184EA7DFCA48268B4FB1A500BFB2A4,6BC6C432FC1640F0A54EF750508D1771,B616ED73F8524954838BB21800957519,CEA8B100E0CA49A997CBAC62FF65CCB7,27645D50664849DD8123E33A75A54F6D,46FEDDE19C23475FBE4B8F34529C2DD5,35D13C4F0F034E6181F2B30600A192AB,E7C68F32E4874B00941AB7A153402B2A,4931992E3FCB4354A8541B79DE7F9301,89EBE90730194A9AAD312E07D0FC0793,F84DB41F570240EB9229B15C010D1415,743F6876706E42538ACBB15C010DE03A,1E446A3F19C542E58E61B1AC01108EA9,10D72BA4993D45D69462B1D100B85619,4D7D923AA1784C49B06CB15D008E96A3,2EF1C91B51BF4ADCBEE1B15D008E77EC,1256EC1B76224B0C9C29B1D5010A7B12,DF645DC4823B4F0D91DC9B2D934157AC,2FAA90C0418440398DEBE130AB23C8E7,77F621C76DF549468A37921FF4F8B38D,0173D402424A47F481B5B2E200916964,0F7BF3643A0549BDBF7AB1A301266140,1396004C6AE44F3AAC12B1A500E41485,D57F5C641ECE49A78A74C6E55416A270,4AE7C4DBF2F3476BBEBB389EB874F5B3,6682F5B20778493481B684CAA8127104,CDFBF68CEE8E4D4A826ED667263E17AC,13D9CFF894384EC18C23AE10FE47A70C,1A4531CCDB4046DEA5FB350561799462,D6B9EC4D9B6543AE9EE40712AC023ADE,0BD1DF582CC94651BE80A0F0AA4C89B5,C1AB22FBC89244BB950BCE5FA245C85A",
    tmp_cust_reg_type_menus: str = "网络部/表单渠道/江苏乐能,南京字广,三灯表单,上海妙茵,掌越,电商/平安保险,抖音团购,高德地图,美团,平安保险,竞价/360,百度,其他渠道/g资源,电话,电话表单,其他资源,市场渠道,网络现场活动,三方/贝色,公众号转诊,鹿之颜,美吧,美程,美呗,小红书,网络部转介绍/客户介绍,员工介绍,信息流/抖音,快手,朋友圈,"

) -> Optional[Dict[str, int]]:
    """
    获取现场咨询来院统计数据

    Args:
        start_date: 开始日期 (格式: YYYY-MM-DD)
        end_date: 结束日期 (格式: YYYY-MM-DD)
        user_token: 用户令牌
        user_name: 用户名
        user_id: 用户ID
        field_consultant_id: 现场顾问ID

    Returns:
        包含统计数据的字典:
        {
            'total_performance': 总开单业绩,
            'total_count': 总人数,
            'deal_count': 成交人数,
            'new_first_count': 新客首次,
            'new_second_count': 新客二次,
            'old_count': 老客
        }
        如果请求失败返回 None
    """

    url = "http://************/Reservation/ToHospital/IndexCount"

    headers = {
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Origin": "http://************",
        "Pragma": "no-cache",
        "Referer": "http://************/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    cookies = {
        "************80_AdminContext_UserToken": user_token,
        "************80_AdminContext_UserName": user_name,
        "************80_AdminContext_UserId": user_id
    }

    # 客户注册类型参数 (从原始curl命令中提取)
    data = {
        "DatetimeRegStart": start_date,
        "DatetimeRegEnd": end_date,
        "TempDatetimeRegStart": "",
        "TempDatetimeRegEnd": "",
        "CustName": "",
        "Phone": "",
        "CustStatus": "",
        "IsDeal": "",
        "IsBookCust": "",
        "SectionId": "",
        "Medias": "",
        "MediasMenus": "",
        "Ptype1": "",
        "Ptype2": "",
        "Ptype3": "",
        "ProductTypeName1s": "",
        "ProductTypeName2s": "",
        "ProductTypeNames": "",
        "TmpCustRegType": tmp_cust_reg_type,
        "TmpCustRegTypeMenus": tmp_cust_reg_type_menus,
        "IsLab": "",
        "CustLabelId": "",
        "CustLabelMenus": "",
        "NoCustLabelId": "",
        "NoCustLabelMenus": "",
        "BookEmpId": "",
        "FieldConsultantId": field_consultant_id,
        "TempCreateBy": "",
        "PlanRecallEmp": "",
        "IsHospSecond": "",
        "Remark": "",
        "Province": "",
        "City": "",
        "Area": "",
        "QQ": "",
        "WeiXinNo": "",
        "GuestId": "",
        "CustCardno": "",
        "Sex": "",
        "TempRecommendEmpId": "",
        "AttentionRemark": "",
        "IsDealCust": "",
        "pageSize": "21",
        "pageCurrent": "1",
        "iscompay": "1",
        "CrossRelation": "",
        "EmployeeToChannel": ""
    }

    try:
        response = requests.post(
            url,
            headers=headers,
            cookies=cookies,
            data=data,
            verify=False  # 对应curl的--insecure参数
        )

        if response.status_code == 200:
            result = response.json()

            if result.get("resStatus") == "1" and result.get("resCode") == "200":
                res_body = result.get("resBody", [])

                # 将数组转换为字典便于查找
                data_dict = {}
                for item in res_body:
                    data_dict[item.get("name")] = int(item.get("value", 0))


                 # 计算一些有用的比率
                if data_dict['count'] > 0:
                    deal_rate = data_dict['dealcount'] / data_dict['count'] * 100
                    data_dict['deal_rate'] = f"{deal_rate:.2f}%"
                    
                    print(f"成交率: {data_dict['deal_rate']}")

                if data_dict['dealcount'] > 0:
                    avg_performance = data_dict['sumbill'] / data_dict['dealcount']
                    data_dict['avg_performance'] = f"{avg_performance:,.2f}"

                return {
                    "total_performance": data_dict.get("sumbill", 0),        # 总开单业绩
                    "total_count": data_dict.get("count", 0),               # 总人数
                    "deal_count": data_dict.get("dealcount", 0),            # 成交人数
                    "new_first_count": data_dict.get("newfirstcount", 0),   # 新客首次
                    "new_second_count": data_dict.get("newsecondcount", 0), # 新客二次
                    "old_count": data_dict.get("oldcount", 0),            # 老客
                    "avg_performance": data_dict.get("avg_performance", 0),  # 平均客单价
                    "deal_rate": data_dict.get("deal_rate", 0),             # 成交率

                }
            else:
                print(f"API返回错误: {result.get('resMessage', '未知错误')}")
                return None
        else:
            print(f"HTTP请求失败: {response.status_code}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"未知错误: {e}")
        return None



def get_all_consultants_stats_to_excel(
    start_date: str,
    end_date: str,
    user_token: str = "AD5B2BE51FEB45E1840FB33E008AA554",
    user_name: str = "ZN009",
    user_id: str = "2ADB7F12A38F4D9680997C4EB0C44496"
) -> str:
    """
    获取所有现场咨询师的统计数据并保存为Excel文件

    Args:
        start_date: 开始日期 (格式: YYYY-MM-DD)
        end_date: 结束日期 (格式: YYYY-MM-DD)
        user_token: 用户令牌
        user_name: 用户名
        user_id: 用户ID

    Returns:
        Excel文件路径
    """

    # 所有现场咨询师信息
    consultants = {
        "曾梦平(ZN341)": "FCB459C4F14043DEB5A8B2F30089014A",
        "程绍婷(ZN174)": "06BF3101ABAD4F0C9F43B1DE0090D166",
        "程耀杰(ZN091)": "C15B7EA5BF6741568A7BB173009F0FED",
        "程媛(ZN161)": "64870963591344DE9379B1CC009A2DE0",
        "丁晓敏(ZN227)": "511681685E624BCC841EB2090116734F",
        "杜明熙(ZN325)": "6F319E6169094374A67BB2AF0110CA98",
        "杜中国(ZN200)": "8D88F0FF09A447BB92DFB1F600AB2E8D",
        "冯程程(ZN090)": "0BCB21C052524860A234B173009EC624",
        "傅妮(ZN089)": "96B8C71889D24E85B710B173009E3743",
        "公账现场咨询师(公账现场咨询师)": "BD60AF6E29F84BE1AE51B161012DD68B",
        "李丽(ZN098)": "5F9A9AC3EC5E4FCC982AB17F00EF3FE8",
        "李炜(ZN258)": "C262821BD2CA4F3E873FB22A00A86DF0",
        "刘梦婷(ZN157)": "DD87B4862FA440929F1FB1BF00E30055",
        "汪克纲(ZN130)": "3419512769F4441AA709B1A1011F9375",
        "王蓉杰(ZN096)": "B4831E86815B4A83A6D9B17600AD06B0",
        "王瑞(ZN003)": "B2A56003475847B0A264C46E3DC9C8FE",
        "吴宇晨(ZN331)": "1990FACE011647C09BC2B2BD00DC6BAE",
        "杨晓梅(ZN330)": "8FCC647F579B47D29444B2BD00DC48B4",
        "杨勇(ZN088)": "D74DAE84EB0F4C2EB7CBB173009D6AC2",
        "尤梦茹(ZN261)": "DF2A6DDA7F9F4CA2B57BB230012E42BE",
        "助理毕文敏(ZN247)": "0F3E67CF5F9542B08EB6B21E00F77984",
        "助理唐金荣(ZN314)": "D0F53B21FF134FCA84D1B29F00960630",
        "助理汪亚莲(ZN194)": "28655077C886400FBCD7B1F300B46B4A",
        "卓逸华(ZN004)": "134D23B5BE2442AFAB852FF7CF9C7593",
        "肖琴(ZN344)": "F536EA35D6B84E3AB820B2F400D69466"
    }

    # 存储所有数据
    all_data = []

    print(f"开始获取 {start_date} 到 {end_date} 的现场咨询统计数据...")

    for consultant_name, consultant_id in consultants.items():
        print(f"正在获取 {consultant_name} 的数据...")

        # 获取该咨询师的数据
        stats = get_consultation_stats(
            start_date=start_date,
            end_date=end_date,
            user_token=user_token,
            user_name=user_name,
            user_id=user_id,
            field_consultant_id=consultant_id
        )
        print(stats)

        if stats:

            # 添加到数据列表
            all_data.append({
                "现场咨询师": consultant_name,
                "总开单业绩": stats['total_performance'],
                "总人数": stats['total_count'],
                "成交人数": stats['deal_count'],
                "成交率(%)":stats['deal_rate'],

                "新客首次": stats['new_first_count'],
                "新客二次": stats['new_second_count'],
                "老客": stats['old_count'],
                "客单价": stats['avg_performance']
            })
        else:
            print(f"获取 {consultant_name} 数据失败")
            # 添加空数据行
            all_data.append({
                "现场咨询师": consultant_name,
                "总开单业绩": 0,
                "总人数": 0,
                "成交人数": 0,
                "成交率(%)": 0,
                "新客首次": 0,
                "新客二次": 0,
                "老客": 0,
                "客单价": 0
            })

    # 创建DataFrame
    df = pd.DataFrame(all_data)

    # 计算汇总行
    total_row = {
        "现场咨询师": "汇总",
        "总开单业绩": df['总开单业绩'].sum(),
        "总人数": df['总人数'].sum(),
        "成交人数": df['成交人数'].sum(),
        "成交率(%)": f"{round((df['成交人数'].sum() / df['总人数'].sum() * 100) if df['总人数'].sum() > 0 else 0, 2)}%",#格式化字符串 百分号后面保留两位小数
        "新客首次": df['新客首次'].sum(),
        "新客二次": df['新客二次'].sum(),
        "老客": df['老客'].sum(),
        "客单价": round((df['总开单业绩'].sum() / df['成交人数'].sum()) if df['成交人数'].sum() > 0 else 0, 2)
    }

    # 添加汇总行
    df = pd.concat([df, pd.DataFrame([total_row])], ignore_index=True)

    # 生成文件名
    filename = f"现场咨询统计_{start_date} 到 {end_date}.xlsx"

    # 保存为Excel文件
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='现场咨询统计', index=False)

        # 获取工作表对象进行格式化
        worksheet = writer.sheets['现场咨询统计']

        # 设置列宽
        column_widths = {
            'A': 25,  # 现场咨询师
            'B': 15,  # 总开单业绩
            'C': 10,  # 总人数
            'D': 10,  # 成交人数
            'E': 12,  # 成交率
            'F': 10,  # 新客首次
            'G': 10,  # 新客二次
            'H': 10,  # 老客
            'I': 12   # 客单价
        }

        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width

    print(f"数据已保存到文件: {filename}")
    print(f"共获取 {len(consultants)} 位现场咨询师的数据")

    return filename


# 示例使用
if __name__ == "__main__":
    # 设置日期范围
    start_time = "2025-08-01"
    end_time = "2025-08-17"

    # 获取所有现场咨询师的统计数据并保存为Excel
    filename = get_all_consultants_stats_to_excel(
        start_date=start_time,
        end_date=end_time
    )

    print(f"\n✅ 所有现场咨询师统计数据已成功导出到: {filename}")
    print("📊 Excel文件包含以下列:")
    print("   - 现场咨询师")
    print("   - 总开单业绩")
    print("   - 总人数")
    print("   - 成交人数")
    print("   - 成交率(%)")
    print("   - 新客首次")
    print("   - 新客二次")
    print("   - 老客")
    print("   - 客单价")
