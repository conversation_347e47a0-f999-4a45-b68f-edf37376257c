// 全局变量
let currentData = null;
let charts = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updateDateTime();
    setInterval(updateDateTime, 1000); // 每秒更新时间
    initCharts();
    loadData();
});

// 更新日期时间显示
function updateDateTime() {
    const now = new Date();
    const dateTimeStr = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    const datetimeElement = document.getElementById('datetime');
    if (datetimeElement) {
        datetimeElement.textContent = dateTimeStr;
    }
}

// 初始化所有图表
function initCharts() {
    charts.pieChart = echarts.init(document.getElementById('pie-chart'));
    charts.customerTypeBar = echarts.init(document.getElementById('customer-type-bar'));
    charts.performanceBar = echarts.init(document.getElementById('performance-bar'));
    charts.dealRateBar = echarts.init(document.getElementById('deal-rate-bar'));
    charts.avgPriceBar = echarts.init(document.getElementById('avg-price-bar'));
    charts.peopleTrend = echarts.init(document.getElementById('people-trend'));
    charts.customerTrend = echarts.init(document.getElementById('customer-trend'));
    charts.dealRateTrend = echarts.init(document.getElementById('deal-rate-trend'));
    charts.avgPriceTrend = echarts.init(document.getElementById('avg-price-trend'));

    // 窗口大小改变时重新调整图表
    window.addEventListener('resize', function() {
        Object.values(charts).forEach(chart => chart.resize());
    });
}

// 加载数据
async function loadData() {
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;

    if (!startDate || !endDate) {
        alert('请选择开始和结束日期');
        return;
    }

    try {
        showLoading();
        const response = await fetch(`/api/stats?start_date=${startDate}&end_date=${endDate}`);
        const data = await response.json();

        if (response.ok) {
            currentData = data;
            updateSummaryCards(data.summary);
            updatePieChart(data.pie_data);
            updateBarCharts(data.bar_data);
            updateConsultantSelector(data.consultants);
        } else {
            showError(data.error || '获取数据失败');
        }
    } catch (error) {
        console.error('Error loading data:', error);
        showError('网络错误，请稍后重试');
    }
}

// 更新汇总卡片
function updateSummaryCards(summary) {
    document.getElementById('total-performance').textContent = formatNumber(summary.total_performance);
    document.getElementById('total-people').textContent = summary.total_people;
    document.getElementById('total-deal').textContent = summary.total_deal;
    document.getElementById('deal-rate').textContent = summary.deal_rate + '%';
    document.getElementById('avg-price').textContent = formatNumber(summary.avg_price);
}

// 更新饼图
function updatePieChart(pieData) {
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left'
        },
        series: [
            {
                name: '客户类型',
                type: 'pie',
                radius: '50%',
                data: pieData,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };
    charts.pieChart.setOption(option);
}

// 更新柱状图
function updateBarCharts(barData) {
    // 客户类型堆叠柱状图
    const customerTypeOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['老客', '新客首次', '新客二次']
        },
        xAxis: {
            type: 'category',
            data: barData.names,
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '老客',
                type: 'bar',
                stack: '客户类型',
                data: barData.old_customer,
                itemStyle: { color: '#5470c6' }
            },
            {
                name: '新客首次',
                type: 'bar',
                stack: '客户类型',
                data: barData.new_first,
                itemStyle: { color: '#91cc75' }
            },
            {
                name: '新客二次',
                type: 'bar',
                stack: '客户类型',
                data: barData.new_second,
                itemStyle: { color: '#fac858' }
            }
        ]
    };
    charts.customerTypeBar.setOption(customerTypeOption);

    // 总开单业绩柱状图
    const performanceOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' + 
                       params[0].seriesName + ': ' + formatNumber(params[0].value);
            }
        },
        xAxis: {
            type: 'category',
            data: barData.names,
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: function(value) {
                    return formatNumber(value);
                }
            }
        },
        series: [
            {
                name: '总开单业绩',
                type: 'bar',
                data: barData.performance,
                itemStyle: { color: '#ee6666' }
            }
        ]
    };
    charts.performanceBar.setOption(performanceOption);

    // 成交率柱状图
    const dealRateOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' + 
                       params[0].seriesName + ': ' + params[0].value + '%';
            }
        },
        xAxis: {
            type: 'category',
            data: barData.names,
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: '{value}%'
            }
        },
        series: [
            {
                name: '成交率',
                type: 'bar',
                data: barData.deal_rate,
                itemStyle: { color: '#73c0de' }
            }
        ]
    };
    charts.dealRateBar.setOption(dealRateOption);

    // 客单价柱状图
    const avgPriceOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' + 
                       params[0].seriesName + ': ' + formatNumber(params[0].value);
            }
        },
        xAxis: {
            type: 'category',
            data: barData.names,
            axisLabel: {
                rotate: 45
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: function(value) {
                    return formatNumber(value);
                }
            }
        },
        series: [
            {
                name: '客单价',
                type: 'bar',
                data: barData.avg_price,
                itemStyle: { color: '#3ba272' }
            }
        ]
    };
    charts.avgPriceBar.setOption(avgPriceOption);
}

// 更新咨询师选择器
function updateConsultantSelector(consultants) {
    const select = document.getElementById('consultant-select');
    select.innerHTML = '<option value="">请选择咨询师</option>';
    
    consultants.forEach(consultant => {
        const option = document.createElement('option');
        option.value = consultant.name;
        option.textContent = consultant.name;
        select.appendChild(option);
    });
}

// 加载趋势数据
async function loadTrendData() {
    const consultantName = document.getElementById('consultant-select').value;
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;

    if (!consultantName) {
        // 清空趋势图
        clearTrendCharts();
        return;
    }

    try {
        const response = await fetch(`/api/consultant_trend?consultant_name=${encodeURIComponent(consultantName)}&start_date=${startDate}&end_date=${endDate}`);
        const data = await response.json();

        if (response.ok) {
            updateTrendCharts(data);
        } else {
            console.error('获取趋势数据失败:', data.error);
        }
    } catch (error) {
        console.error('Error loading trend data:', error);
    }
}

// 更新趋势图表
function updateTrendCharts(trendData) {
    // 总人数趋势
    const peopleTrendOption = {
        tooltip: {
            trigger: 'axis'
        },
        xAxis: {
            type: 'category',
            data: trendData.dates
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '总人数',
                type: 'line',
                data: trendData.total_people,
                smooth: true
            }
        ]
    };
    charts.peopleTrend.setOption(peopleTrendOption);

    // 客户类型趋势
    const customerTrendOption = {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['老客', '新客首次', '新客二次']
        },
        xAxis: {
            type: 'category',
            data: trendData.dates
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '老客',
                type: 'line',
                data: trendData.old_customer,
                smooth: true
            },
            {
                name: '新客首次',
                type: 'line',
                data: trendData.new_first,
                smooth: true
            },
            {
                name: '新客二次',
                type: 'line',
                data: trendData.new_second,
                smooth: true
            }
        ]
    };
    charts.customerTrend.setOption(customerTrendOption);

    // 成交率趋势
    const dealRateTrendOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' + 
                       params[0].seriesName + ': ' + params[0].value + '%';
            }
        },
        xAxis: {
            type: 'category',
            data: trendData.dates
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: '{value}%'
            }
        },
        series: [
            {
                name: '成交率',
                type: 'line',
                data: trendData.deal_rate,
                smooth: true
            }
        ]
    };
    charts.dealRateTrend.setOption(dealRateTrendOption);

    // 客单价趋势
    const avgPriceTrendOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                return params[0].name + '<br/>' + 
                       params[0].seriesName + ': ' + formatNumber(params[0].value);
            }
        },
        xAxis: {
            type: 'category',
            data: trendData.dates
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: function(value) {
                    return formatNumber(value);
                }
            }
        },
        series: [
            {
                name: '客单价',
                type: 'line',
                data: trendData.avg_price,
                smooth: true
            }
        ]
    };
    charts.avgPriceTrend.setOption(avgPriceTrendOption);
}

// 清空趋势图表
function clearTrendCharts() {
    const emptyOption = {
        title: {
            text: '请选择咨询师',
            left: 'center',
            top: 'center',
            textStyle: {
                color: '#999'
            }
        }
    };
    
    charts.peopleTrend.setOption(emptyOption);
    charts.customerTrend.setOption(emptyOption);
    charts.dealRateTrend.setOption(emptyOption);
    charts.avgPriceTrend.setOption(emptyOption);
}

// 工具函数
function formatNumber(num) {
    if (num === null || num === undefined || isNaN(num)) return '0';
    return new Intl.NumberFormat('zh-CN').format(Math.round(num));
}

function showLoading() {
    // 可以添加加载动画
    console.log('Loading...');
}

function showError(message) {
    alert('错误: ' + message);
}
