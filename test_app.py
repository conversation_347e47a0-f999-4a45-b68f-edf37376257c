#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Flask应用
"""

from flask import Flask, render_template, jsonify
import json
from datetime import datetime, timedelta

app = Flask(__name__)

@app.route('/')
def index():
    """首页"""
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
    
    return render_template('consultant_stats.html', 
                         start_date=start_date, 
                         end_date=end_date)

@app.route('/api/stats')
def get_stats():
    """获取统计数据API - 返回模拟数据"""
    # 模拟数据
    mock_data = {
        "summary": {
            "total_performance": 1500000,
            "total_people": 150,
            "total_deal": 45,
            "deal_rate": 30.0,
            "avg_price": 33333
        },
        "pie_data": [
            {"name": "老客", "value": 20},
            {"name": "新客首次", "value": 80},
            {"name": "新客二次", "value": 50}
        ],
        "consultants": [
            {
                "name": "张三(ZN001)",
                "total_performance": 300000,
                "total_people": 30,
                "deal_people": 9,
                "deal_rate": 30.0,
                "avg_price": 33333,
                "old_customer": 5,
                "new_first": 15,
                "new_second": 10
            },
            {
                "name": "李四(ZN002)",
                "total_performance": 250000,
                "total_people": 25,
                "deal_people": 8,
                "deal_rate": 32.0,
                "avg_price": 31250,
                "old_customer": 3,
                "new_first": 12,
                "new_second": 10
            }
        ],
        "bar_data": {
            "names": ["张三(ZN001)", "李四(ZN002)"],
            "performance": [300000, 250000],
            "deal_rate": [30.0, 32.0],
            "avg_price": [33333, 31250],
            "old_customer": [5, 3],
            "new_first": [15, 12],
            "new_second": [10, 10]
        }
    }
    
    return jsonify(mock_data)

@app.route('/api/consultant_trend')
def get_consultant_trend():
    """获取指定咨询师的趋势数据 - 返回模拟数据"""
    # 模拟趋势数据
    trend_data = {
        "dates": ["2025-08-10", "2025-08-11", "2025-08-12", "2025-08-13", "2025-08-14"],
        "total_people": [5, 8, 6, 7, 9],
        "old_customer": [1, 2, 1, 2, 3],
        "new_first": [3, 4, 3, 3, 4],
        "new_second": [1, 2, 2, 2, 2],
        "deal_rate": [20, 25, 33, 28, 33],
        "avg_price": [30000, 32000, 35000, 31000, 34000]
    }
    
    return jsonify(trend_data)

if __name__ == '__main__':
    print("=" * 50)
    print("测试应用启动")
    print("访问地址: http://localhost:5002")
    print("=" * 50)
    app.run(debug=True, host='0.0.0.0', port=5002)
