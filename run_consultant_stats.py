#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启动现场咨询师来院数据统计Web应用
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from consultant_stats_app import app

if __name__ == '__main__':
    print("=" * 50)
    print("现场咨询师来院数据统计系统")
    print("=" * 50)
    print("启动Web服务器...")
    print("访问地址: http://localhost:5001")
    print("按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    try:
        app.run(debug=True, host='0.0.0.0', port=5001)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        print("请检查端口5001是否被占用")
