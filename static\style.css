* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    background-color: #f0f2f5;
}

.container {
    max-width: 100%;
    padding: 15px;
    padding-top: 20px;
}

h1 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
}

.data-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    min-height: 120px;
}

.card h3 {
    font-size: 16px;
    color: #666;
    margin-bottom: 15px;
}

.card p {
    font-size: 32px;
    color: #ff4d4f;
    font-weight: 700;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', monospace, sans-serif;
    text-shadow: 0 0 2px rgba(255, 77, 79, 0.2);
    margin: 0;
    line-height: 1.2;
    letter-spacing: 1px;
    animation: glow 1.5s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        text-shadow: 0 0 2px rgba(255, 77, 79, 0.2);
    }
    to {
        text-shadow: 0 0 4px rgba(255, 77, 79, 0.4),
                     0 0 6px rgba(255, 77, 79, 0.2);
    }
}

.charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.chart {
    height: 300px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .data-cards {
        grid-template-columns: 1fr;
    }
    
    .charts {
        grid-template-columns: 1fr;
    }
}

.bar-chart-container {
    margin-top: 20px;
}

.bar-chart {
    height: 400px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.bar-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.bar-chart {
    height: 400px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@media (max-width: 1200px) {
    .bar-charts {
        grid-template-columns: 1fr;
    }
}

.tab-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    background-color: #f0f2f5;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    white-space: nowrap;
    margin-right: 10px;
    margin-bottom: 10px;
}

.tab-btn:hover {
    background-color: #e6e8eb;
}

.tab-btn.active {
    background-color: #ff4d4f;
    color: white;
}

@media (max-width: 768px) {
    .container {
        margin-top: 80px;
    }
}

/* 添加渠道 tab 样式 */
.channel-tabs {
    margin: 20px 0;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 10px;
}

.channel-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    background-color: #f0f2f5;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    margin-right: 10px;
    margin-bottom: 10px;
}

.channel-btn:hover {
    background-color: #e6e8eb;
}

.channel-btn.active {
    background-color: #ff4d4f;
    color: white;
}

/* 投产比颜色根据数值变化 */
.roi-good {
    color: #52c41a !important;  /* 绿色 */
}

.roi-normal {
    color: #faad14 !important;  /* 黄色 */
}

.roi-bad {
    color: #ff4d4f !important;  /* 红色 */
}

.line-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.line-chart {
    height: 400px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@media (max-width: 1200px) {
    .line-charts {
        grid-template-columns: 1fr;
    }
}

.channel-btn[data-channel="合计"] {
    background-color: #1890ff;
    color: white;
}

.channel-btn[data-channel="合计"]:hover {
    background-color: #096dd9;
}

.channel-btn[data-channel="合计"].active {
    background-color: #096dd9;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* 成交人数柱状图区域 - 单独一行 */
.deal-count-section {
    margin-top: 20px;
}

.deal-count-section .chart-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
}

.deal-count-section .chart-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.deal-count-section .chart {
    height: 400px;
}

/* 模态框样式 */
.modal {
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #fff;
    border-radius: 10px;
    width: 90%;
    max-width: 800px;
    max-height: 80%;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background-color: #007bff;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
}

.close {
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

.report-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.report-list-header h4 {
    margin: 0;
    color: #333;
}

.refresh-btn {
    background-color: #17a2b8;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
}

.refresh-btn:hover {
    background-color: #138496;
}

.report-list {
    max-height: 400px;
    overflow-y: auto;
}

.report-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
}

.report-info {
    flex: 1;
}

.report-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.report-details {
    font-size: 12px;
    color: #666;
}

.report-actions {
    display: flex;
    gap: 10px;
}

.download-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.download-btn:hover {
    background-color: #218838;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.delete-btn:hover {
    background-color: #c82333;
}

.loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

.no-reports {
    text-align: center;
    padding: 20px;
    color: #999;
}