import pandas as pd
import os
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.naive_bayes import MultinomialNB
import re
import jieba

def preprocess_text(text):
    if not isinstance(text, str):
        return ""
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9]', '', text)
    words = jieba.cut(text)
    return ' '.join(words)

def check_and_classify(row):
    create_time = pd.to_datetime(row['线索创建时间'])
    record_time = pd.to_datetime(row['建档时间'])
    channel = str(row['建档渠道'])
    if record_time < create_time and (not pd.isna(channel) and channel.strip() != '' and '抖音' not in channel):
        return "重单"
    if row['是否到院'] == '是':
        return "有效"
    if row['最后回访记录'] == '没有回访记录':
        return "没有回访记录"
    return "待分类"

def classify_data(filename):
    try:
        # 读取上传的文件
        original_data = pd.read_excel(filename)
        
        # 保存没有回访记录的数据
        original_data['备注'] = original_data.apply(check_and_classify, axis=1)
        no_record_data = original_data[original_data['备注'] != "待分类"].copy()
        test_data = original_data[original_data['备注'] == "待分类"].copy()
        
        # 文本预处理
        train_data = pd.read_excel('训练集.xlsx')
        train_data['processed_text'] = train_data['最后回访记录'].apply(preprocess_text)
        test_data['processed_text'] = test_data['最后回访记录'].apply(preprocess_text)
        
        # 对于特定的短文本，增加权重
        def enhance_short_text(text, original):
            if not isinstance(original, str):
                return text
            if len(original.strip()) <= 5:
                return text + ' ' + text + ' ' + text
            return text
        
        train_data['processed_text'] = train_data.apply(
            lambda x: enhance_short_text(x['processed_text'], x['最后回访记录']), axis=1
        )
        test_data['processed_text'] = test_data.apply(
            lambda x: enhance_short_text(x['processed_text'], x['最后回访记录']), axis=1
        )
        
        # 准备训练数据
        X_train = train_data['processed_text']
        y_train = train_data['备注']
        
        # 文本向量化
        vectorizer = TfidfVectorizer(
            min_df=1,
            max_features=10000,
            ngram_range=(1, 3),
            analyzer='char_wb',
        )
        X_train_vec = vectorizer.fit_transform(X_train)
        
        # 训练模型
        model = MultinomialNB(alpha=0.01)
        model.fit(X_train_vec, y_train)
        
        # 对测试数据进行预测
        X_test_vec = vectorizer.transform(test_data['processed_text'])
        predictions = model.predict(X_test_vec)
        
        # 添加预测概率
        probabilities = model.predict_proba(X_test_vec)
        max_probs = probabilities.max(axis=1)
        
        # 对于完全匹配的短文本，直接使用训练集中的标签
        exact_matches = {}
        for _, row in train_data.iterrows():
            if len(row['最后回访记录'].strip()) <= 5:
                exact_matches[row['最后回访记录'].strip()] = row['备注']
        
        # 将预测结果和概率添加到结果中
        test_data['备注'] = predictions
        test_data['预测概率'] = max_probs
        
        # 处理完全匹配的情况
        for idx, row in test_data.iterrows():
            if row['最后回访记录'].strip() in exact_matches:
                test_data.loc[idx, '备注'] = exact_matches[row['最后回访记录'].strip()]
                test_data.loc[idx, '预测概率'] = 1.0
        
        # 只有当预测概率大于阈值时才采用预测结果
        threshold = 0.5
        test_data.loc[test_data['预测概率'] < threshold, '备注'] = '需人工审核'
        
        # 合并预测结果和没有回访记录的数据
        final_result = pd.concat([test_data, no_record_data], ignore_index=True)
        
        # 保存结果
        output_filename = os.path.join('results', f'classified_{os.path.basename(filename)}')
        final_result.to_excel(output_filename, index=False)
        
        # 模拟分类完成后的通知
        # time.sleep(5)  # 假设分类需要5秒
        print(f"Classification completed. Result saved as {output_filename}")
    except Exception as e:
        print(f"Error during classification: {e}")
