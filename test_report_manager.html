<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报表管理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-section h3 {
            color: #007bff;
            margin-bottom: 15px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .download-btn {
            background-color: #28a745;
        }
        .download-btn:hover {
            background-color: #218838;
        }
        .delete-btn {
            background-color: #dc3545;
        }
        .delete-btn:hover {
            background-color: #c82333;
        }
        #result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #e9ecef;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .file-list {
            margin-top: 15px;
        }
        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin-bottom: 5px;
            background-color: white;
        }
        .file-info {
            flex: 1;
        }
        .file-name {
            font-weight: bold;
            color: #333;
        }
        .file-details {
            font-size: 12px;
            color: #666;
        }
        .file-actions {
            display: flex;
            gap: 5px;
        }
        .file-actions button {
            padding: 5px 10px;
            font-size: 12px;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>报表管理功能测试</h1>
        
        <div class="test-section">
            <h3>1. 获取报表列表</h3>
            <button onclick="testGetReports()">获取报表列表</button>
            <div id="fileList" class="file-list"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 测试下载功能</h3>
            <p>点击上面列表中的"下载"按钮测试下载功能</p>
        </div>
        
        <div class="test-section">
            <h3>3. 测试删除功能</h3>
            <p>点击上面列表中的"删除"按钮测试删除功能（请谨慎操作）</p>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        async function testGetReports() {
            const result = document.getElementById('result');
            const fileList = document.getElementById('fileList');
            
            try {
                result.textContent = '正在获取报表列表...';
                
                const response = await fetch('/api/reports');
                const data = await response.json();
                
                if (data.success) {
                    result.textContent = `成功获取 ${data.count} 个报表文件`;
                    displayFiles(data.files);
                } else {
                    result.textContent = '获取失败: ' + data.error;
                    fileList.innerHTML = '';
                }
            } catch (error) {
                result.textContent = '请求失败: ' + error.message;
                fileList.innerHTML = '';
            }
        }
        
        function displayFiles(files) {
            const fileList = document.getElementById('fileList');
            
            if (files.length === 0) {
                fileList.innerHTML = '<p>暂无报表文件</p>';
                return;
            }
            
            let html = '';
            files.forEach(file => {
                html += `
                    <div class="file-item">
                        <div class="file-info">
                            <div class="file-name">${file.filename}</div>
                            <div class="file-details">
                                大小: ${file.size_mb} MB | 修改时间: ${file.modified_time}
                            </div>
                        </div>
                        <div class="file-actions">
                            <button class="download-btn" onclick="downloadFile('${file.filename}')">
                                下载
                            </button>
                            <button class="delete-btn" onclick="deleteFile('${file.filename}')">
                                删除
                            </button>
                        </div>
                    </div>
                `;
            });
            
            fileList.innerHTML = html;
        }
        
        function downloadFile(filename) {
            const result = document.getElementById('result');
            result.textContent = `正在下载: ${filename}`;
            
            const link = document.createElement('a');
            link.href = `/api/reports/download/${encodeURIComponent(filename)}`;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            setTimeout(() => {
                result.textContent = `下载已开始: ${filename}`;
            }, 500);
        }
        
        async function deleteFile(filename) {
            if (!confirm(`确定要删除文件 "${filename}" 吗？此操作不可恢复！`)) {
                return;
            }
            
            const result = document.getElementById('result');
            
            try {
                result.textContent = `正在删除: ${filename}`;
                
                const response = await fetch(`/api/reports/delete/${encodeURIComponent(filename)}`, {
                    method: 'DELETE'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    result.textContent = `删除成功: ${filename}`;
                    testGetReports(); // 重新加载列表
                } else {
                    result.textContent = `删除失败: ${data.error}`;
                }
            } catch (error) {
                result.textContent = `删除失败: ${error.message}`;
            }
        }
        
        // 页面加载时自动获取报表列表
        window.onload = function() {
            testGetReports();
        };
    </script>
</body>
</html>
