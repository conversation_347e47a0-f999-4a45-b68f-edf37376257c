#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网电咨询师测试服务器 - 使用真实数据
"""

from flask import Flask, render_template, request, jsonify

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('dashboard_fullscreen.html', 
                         start_date='2025-08-15', 
                         end_date='2025-08-21')

@app.route('/dashboard')
def dashboard():
    return render_template('dashboard_fullscreen.html', 
                         start_date='2025-08-15', 
                         end_date='2025-08-21')

@app.route('/api/stats')
def get_stats():
    """现场咨询师API - 使用真实数据"""
    start_date = request.args.get('start_date', '2025-08-15')
    end_date = request.args.get('end_date', '2025-08-21')
    
    print(f"现场咨询师API调用: {start_date} 到 {end_date}")
    
    try:
        # 尝试获取真实数据
        from consultant_stats_app import get_consultant_stats_data
        
        data = get_consultant_stats_data(start_date, end_date)
        
        if data:
            print(f"返回现场咨询师真实数据: {len(data.get('consultants', []))} 个咨询师")
            return jsonify(data)
        else:
            print("获取现场咨询师数据失败，使用模拟数据")
            raise Exception("获取真实数据失败")
            
    except Exception as e:
        print(f"现场咨询师API异常: {e}，使用模拟数据")
        
        # 如果获取真实数据失败，使用模拟数据
        data = {
            'summary': {
                'total_performance': 850000.0,
                'total_people': 420,
                'total_deal': 65,
                'deal_rate': 15.48,
                'avg_price': 13076.92
            },
            'consultants': [
                {'name': '杜中国(ZN200)', 'total_performance': 120000, 'total_people': 87, 'deal_people': 15, 'deal_rate': 17.24, 'avg_price': 8000, 'old_customer': 30, 'new_first': 45, 'new_second': 12},
                {'name': '程绍婷(ZN174)', 'total_performance': 95000, 'total_people': 65, 'deal_people': 12, 'deal_rate': 18.46, 'avg_price': 7916, 'old_customer': 20, 'new_first': 35, 'new_second': 10},
                {'name': '王瑞(ZN003)', 'total_performance': 110000, 'total_people': 78, 'deal_people': 14, 'deal_rate': 17.95, 'avg_price': 7857, 'old_customer': 23, 'new_first': 40, 'new_second': 15},
            ],
            'pie_data': [
                {'name': '老客', 'value': 150},
                {'name': '新客首次', 'value': 200},
                {'name': '新客二次', 'value': 70}
            ],
            'bar_data': {
                'names': ['杜中国(ZN200)', '程绍婷(ZN174)', '王瑞(ZN003)'],
                'performance': [120000, 95000, 110000],
                'deal_count': [15, 12, 14],
                'deal_rate': [17.24, 18.46, 17.95],
                'avg_price': [8000, 7916, 7857],
                'old_customer': [30, 20, 23],
                'new_first': [45, 35, 40],
                'new_second': [12, 10, 15]
            }
        }
        
        print(f"返回现场咨询师模拟数据: {len(data['consultants'])} 个咨询师")
        return jsonify(data)

@app.route('/api/wdzx_stats')
def get_wdzx_stats():
    """网电咨询师API - 使用真实数据"""
    start_date = request.args.get('start_date', '2025-08-15')
    end_date = request.args.get('end_date', '2025-08-21')
    
    print(f"网电咨询师API调用: {start_date} 到 {end_date}")
    
    try:
        # 尝试获取真实数据
        from 网电咨询师来院统计 import get_all_wdzx_consultants_stats
        
        print("正在获取网电咨询师真实数据...")
        wdzx_data = get_all_wdzx_consultants_stats(
            start_date=start_date,
            end_date=end_date
        )
        
        if wdzx_data and 'error' not in wdzx_data:
            print(f"获取到网电咨询师原始数据: {wdzx_data}")
            
            # 转换数据格式
            summary = wdzx_data['summary']
            consultants_raw = wdzx_data.get('consultants', [])
            
            # 转换汇总数据
            total_people = summary.get('total_count', 0)
            total_deal = summary.get('deal_count', 0)
            deal_rate = (total_deal / total_people * 100) if total_people > 0 else 0
            avg_price = (summary.get('total_performance', 0) / total_deal) if total_deal > 0 else 0
            
            # 转换咨询师数据
            consultants = []
            for consultant in consultants_raw:
                consultant_people = consultant.get('total_count', 0)
                consultant_deal = consultant.get('deal_count', 0)
                consultant_rate = (consultant_deal / consultant_people * 100) if consultant_people > 0 else 0
                consultant_avg_price = (consultant.get('total_performance', 0) / consultant_deal) if consultant_deal > 0 else 0
                
                consultants.append({
                    "name": consultant.get('consultant_name', ''),
                    "total_performance": float(consultant.get('total_performance', 0)),
                    "total_people": int(consultant_people),
                    "deal_people": int(consultant_deal),
                    "deal_rate": round(consultant_rate, 2),
                    "avg_price": round(consultant_avg_price, 2),
                    "old_customer": int(consultant.get('old_count', 0)),
                    "new_first": int(consultant.get('new_first_count', 0)),
                    "new_second": int(consultant.get('new_second_count', 0))
                })
            
            # 构建返回数据
            data = {
                "summary": {
                    "total_performance": float(summary.get('total_performance', 0)),
                    "total_people": int(total_people),
                    "total_deal": int(total_deal),
                    "deal_rate": round(deal_rate, 2),
                    "avg_price": round(avg_price, 2)
                },
                "consultants": consultants,
                "pie_data": [
                    {"name": "老客", "value": int(summary.get('old_count', 0))},
                    {"name": "新客首次", "value": int(summary.get('new_first_count', 0))},
                    {"name": "新客二次", "value": int(summary.get('new_second_count', 0))}
                ],
                "bar_data": {
                    "names": [c["name"] for c in consultants],
                    "performance": [c["total_performance"] for c in consultants],
                    "deal_count": [c["deal_people"] for c in consultants],
                    "deal_rate": [c["deal_rate"] for c in consultants],
                    "avg_price": [c["avg_price"] for c in consultants],
                    "old_customer": [c["old_customer"] for c in consultants],
                    "new_first": [c["new_first"] for c in consultants],
                    "new_second": [c["new_second"] for c in consultants]
                }
            }
            
            print(f"返回网电咨询师真实数据: {len(data['consultants'])} 个咨询师")
            return jsonify(data)
        else:
            print("获取网电咨询师数据失败，使用模拟数据")
            raise Exception("获取真实数据失败")
            
    except Exception as e:
        print(f"网电咨询师API异常: {e}，使用模拟数据")
        import traceback
        traceback.print_exc()
        
        # 如果获取真实数据失败，使用模拟数据
        data = {
            'summary': {
                'total_performance': 650000.0,
                'total_people': 320,
                'total_deal': 48,
                'deal_rate': 15.0,
                'avg_price': 13541.67
            },
            'consultants': [
                {'name': '张三(WD001)', 'total_performance': 85000, 'total_people': 55, 'deal_people': 8, 'deal_rate': 14.55, 'avg_price': 10625, 'old_customer': 17, 'new_first': 30, 'new_second': 8},
                {'name': '李四(WD002)', 'total_performance': 92000, 'total_people': 62, 'deal_people': 10, 'deal_rate': 16.13, 'avg_price': 9200, 'old_customer': 15, 'new_first': 35, 'new_second': 12},
                {'name': '王五(WD003)', 'total_performance': 78000, 'total_people': 48, 'deal_people': 7, 'deal_rate': 14.58, 'avg_price': 11142, 'old_customer': 15, 'new_first': 25, 'new_second': 8},
            ],
            'pie_data': [
                {'name': '老客', 'value': 100},
                {'name': '新客首次', 'value': 160},
                {'name': '新客二次', 'value': 60}
            ],
            'bar_data': {
                'names': ['张三(WD001)', '李四(WD002)', '王五(WD003)'],
                'performance': [85000, 92000, 78000],
                'deal_count': [8, 10, 7],
                'deal_rate': [14.55, 16.13, 14.58],
                'avg_price': [10625, 9200, 11142],
                'old_customer': [17, 15, 15],
                'new_first': [30, 35, 25],
                'new_second': [8, 12, 8]
            }
        }
        
        print(f"返回网电咨询师模拟数据: {len(data['consultants'])} 个咨询师")
        return jsonify(data)

@app.route('/api/consultant_trend')
def get_consultant_trend():
    """现场咨询师趋势"""
    print("现场咨询师趋势API调用")
    
    data = {
        'dates': ['2025-08-16', '2025-08-23'],
        'total_people': [200, 220],
        'old_customer': [80, 90],
        'new_first': [90, 100],
        'new_second': [30, 30],
        'deal_rate': [15.5, 16.8],
        'avg_price': [8500, 8200],
        'total_performance': [425000, 425000]
    }
    
    return jsonify(data)

@app.route('/api/wdzx_consultant_trend')
def get_wdzx_consultant_trend():
    """网电咨询师趋势"""
    print("网电咨询师趋势API调用")
    
    data = {
        'dates': ['2025-08-16', '2025-08-23'],
        'total_people': [150, 170],
        'old_customer': [60, 70],
        'new_first': [70, 80],
        'new_second': [20, 20],
        'deal_rate': [14.0, 15.3],
        'avg_price': [9500, 9200],
        'total_performance': [325000, 350000]
    }
    
    return jsonify(data)

if __name__ == '__main__':
    print("启动网电咨询师测试服务器...")
    print("访问地址: http://localhost:5002/dashboard")
    app.run(debug=True, host='0.0.0.0', port=5002)
