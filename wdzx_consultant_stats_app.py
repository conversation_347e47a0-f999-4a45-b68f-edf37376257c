#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网电咨询师来院数据统计Web应用
"""

from flask import Flask, render_template, request, jsonify, send_file, abort
import pandas as pd
import json
from datetime import datetime, timedelta
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入数据获取函数
from 网电咨询师来院统计 import get_all_wdzx_consultants_stats_to_excel

app = Flask(__name__)

def get_wdzx_consultant_stats_data(start_date, end_date):
    """
    获取网电咨询师统计数据
    
    Args:
        start_date (str): 开始日期
        end_date (str): 结束日期
    
    Returns:
        dict: 统计数据
    """
    try:
        # 调用原有函数获取Excel文件
        excel_file=f"网电咨询统计_{start_date} 到 {end_date}.xlsx"
        if not excel_file or not os.path.exists(excel_file):
            excel_file = get_all_wdzx_consultants_stats_to_excel(start_date, end_date)
        
        if not excel_file or not os.path.exists(excel_file):
            return None
        
        # 读取Excel文件
        df = pd.read_excel(excel_file)

        # 处理数据
        stats_data = {
            "summary": {},
            "consultants": [],
            "pie_data": [],
            "bar_data": {},
            "line_data": {}
        }

        # 排除汇总行，只处理咨询师的数据
        consultant_df = df[df['网电咨询师'] != '汇总'].copy()

        # 直接使用Excel文件中的汇总行数据
        summary_rows = df[df['网电咨询师'] == '汇总']
        if len(summary_rows) > 0:
            summary_row = summary_rows.iloc[0]
            total_performance = float(summary_row['总开单业绩'])
            total_people = int(summary_row['总人数'])
            total_deal = int(summary_row['成交人数'])
            deal_rate = float((total_deal / total_people * 100) if total_people > 0 else 0)
            avg_price = float((total_performance / total_deal) if total_deal > 0 else 0)

            # 汇总行的客户类型数据
            old_customer = int(summary_row['老客'])
            new_first = int(summary_row['新客首次'])
            new_second = int(summary_row['新客二次'])
        else:
            # 如果没有汇总行，则计算汇总数据
            total_performance = float(consultant_df['总开单业绩'].sum())
            total_people = int(consultant_df['总人数'].sum())
            total_deal = int(consultant_df['成交人数'].sum())
            deal_rate = float((total_deal / total_people * 100) if total_people > 0 else 0)
            avg_price = float((total_performance / total_deal) if total_deal > 0 else 0)

            # 计算客户类型数据
            old_customer = int(consultant_df['老客'].sum())
            new_first = int(consultant_df['新客首次'].sum())
            new_second = int(consultant_df['新客二次'].sum())

        stats_data["summary"] = {
            "total_performance": total_performance,
            "total_people": total_people,
            "total_deal": total_deal,
            "deal_rate": round(deal_rate, 2),
            "avg_price": round(avg_price, 2)
        }

        stats_data["pie_data"] = [
            {"name": "老客", "value": old_customer},
            {"name": "新客首次", "value": new_first},
            {"name": "新客二次", "value": new_second}
        ]
        
        # 处理每个咨询师的数据（使用已经排除汇总行的DataFrame）
        for _, row in consultant_df.iterrows():
            consultant_name = row['网电咨询师']
            if pd.isna(consultant_name):
                continue

            # 处理客单价字段，可能包含逗号分隔符
            avg_price_str = str(row['客单价'])
            if avg_price_str and avg_price_str != '0' and avg_price_str != 'nan':
                # 移除逗号并转换为浮点数
                avg_price = float(avg_price_str.replace(',', ''))
            else:
                # 如果客单价为空或0，则计算
                avg_price = round(float((row['总开单业绩'] / row['成交人数']) if row['成交人数'] > 0 else 0), 2)

            consultant_data = {
                "name": consultant_name,
                "total_performance": float(row['总开单业绩']),
                "total_people": int(row['总人数']),
                "deal_people": int(row['成交人数']),
                "deal_rate": round(float((row['成交人数'] / row['总人数'] * 100) if row['总人数'] > 0 else 0), 2),
                "avg_price": avg_price,
                "old_customer": int(row['老客']),
                "new_first": int(row['新客首次']),
                "new_second": int(row['新客二次'])
            }
            stats_data["consultants"].append(consultant_data)
        
        # 柱状图数据
        stats_data["bar_data"] = {
            "names": [c["name"] for c in stats_data["consultants"]],
            "performance": [c["total_performance"] for c in stats_data["consultants"]],
            "deal_count": [c["deal_people"] for c in stats_data["consultants"]],
            "deal_rate": [c["deal_rate"] for c in stats_data["consultants"]],
            "avg_price": [c["avg_price"] for c in stats_data["consultants"]],
            "old_customer": [c["old_customer"] for c in stats_data["consultants"]],
            "new_first": [c["new_first"] for c in stats_data["consultants"]],
            "new_second": [c["new_second"] for c in stats_data["consultants"]]
        }
        
        return stats_data
        
    except Exception as e:
        print(f"获取数据失败: {e}")
        return None

@app.route('/')
def index():
    """首页"""
    # 默认显示最近7天的数据
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

    return render_template('wdzx_consultant_stats.html',
                         start_date=start_date,
                         end_date=end_date)

@app.route('/dashboard')
def dashboard():
    """大屏数据可视化页面"""
    # 默认显示最近14天的数据
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=14)).strftime('%Y-%m-%d')

    return render_template('wdzx_dashboard_fullscreen.html',
                         start_date=start_date,
                         end_date=end_date)

@app.route('/test')
def test_api():
    """API测试页面"""
    html_content = '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>网电咨询师API测试</title>
    </head>
    <body>
        <h1>网电咨询师API测试页面</h1>
        <button onclick="testAPI()">测试API</button>
        <div id="result"></div>
        <script>
            async function testAPI() {
                const resultDiv = document.getElementById('result');
                resultDiv.innerHTML = '正在测试...';
                try {
                    const response = await fetch('/api/stats?start_date=2025-08-01&end_date=2025-08-18');
                    const data = await response.json();
                    if (response.ok) {
                        resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    } else {
                        resultDiv.innerHTML = '错误: ' + (data.error || '未知错误');
                    }
                } catch (error) {
                    resultDiv.innerHTML = '网络错误: ' + error.message;
                }
            }
        </script>
    </body>
    </html>
    '''
    return html_content

@app.route('/api/stats')
def get_stats():
    """获取统计数据API"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    print(f"API调用: start_date={start_date}, end_date={end_date}")

    if not start_date or not end_date:
        print("错误: 缺少日期参数")
        return jsonify({"error": "缺少日期参数"}), 400

    try:
        data = get_wdzx_consultant_stats_data(start_date, end_date)
        print(f"获取到数据: {data is not None}")

        if data is None:
            print("错误: 获取数据失败")
            return jsonify({"error": "获取数据失败"}), 500

        print(f"返回数据，咨询师数量: {len(data.get('consultants', []))}")
        return jsonify(data)
    except Exception as e:
        print(f"API异常: {e}")
        return jsonify({"error": f"服务器错误: {str(e)}"}), 500

@app.route('/api/consultant_trend')
def get_consultant_trend():
    """获取指定咨询师的趋势数据"""
    try:
        consultant_name = request.args.get('consultant_name')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        interval_days = int(request.args.get('interval_days', 14))

        print(f"趋势数据请求: consultant_name={consultant_name}, start_date={start_date}, end_date={end_date}, interval_days={interval_days}")

        if not all([start_date, end_date]):
            return jsonify({"error": "缺少日期参数"}), 400

        # 生成时间间隔的趋势数据
        trend_data = generate_wdzx_trend_data(start_date, end_date, interval_days, consultant_name)
        print(f"趋势数据生成完成: {len(trend_data.get('dates', []))} 个数据点")

        return jsonify(trend_data)
    except Exception as e:
        print(f"趋势数据API异常: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"服务器错误: {str(e)}"}), 500

def generate_wdzx_trend_data(start_date, end_date, interval_days, consultant_name=None):
    """
    生成网电咨询师趋势数据 - 基于真实Excel数据

    Args:
        start_date (str): 开始日期
        end_date (str): 结束日期
        interval_days (int): 时间间隔天数
        consultant_name (str): 咨询师名称，可选

    Returns:
        dict: 趋势数据
    """
    from datetime import datetime, timedelta

    # 解析日期
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d')

    dates = []
    total_people = []
    old_customer = []
    new_first = []
    new_second = []
    deal_rate = []
    avg_price = []
    total_performance = []

    # 从开始日期开始，按间隔生成数据点
    current_date = start_dt

    while current_date <= end_dt:
        # 计算当前时间段的结束日期
        period_end_date = min(current_date + timedelta(days=interval_days - 1), end_dt)

        # 为每个时间段单独获取数据
        period_start_str = current_date.strftime('%Y-%m-%d')
        period_end_str = period_end_date.strftime('%Y-%m-%d')

        print(f"获取时间段数据: {period_start_str} 到 {period_end_str}")
        period_data = get_wdzx_consultant_stats_data(period_start_str, period_end_str)
        if period_data:
            # 使用时间段的中点作为显示日期
            mid_date = current_date + timedelta(days=interval_days // 2)
            date_str = mid_date.strftime('%Y-%m-%d')
            dates.append(date_str)

            if consultant_name:
                # 如果指定了咨询师，查找该咨询师的数据
                consultant_data = None
                for consultant in period_data["consultants"]:
                    # 支持精确匹配和包含匹配
                    if consultant["name"] == consultant_name or consultant_name in consultant["name"]:
                        consultant_data = consultant
                        print(f"找到咨询师数据: {consultant['name']} - 时间段 {period_start_str}~{period_end_str} - 总人数: {consultant['total_people']}")
                        break

                if consultant_data:
                    total_people.append(consultant_data["total_people"])
                    old_customer.append(consultant_data["old_customer"])
                    new_first.append(consultant_data["new_first"])
                    new_second.append(consultant_data["new_second"])
                    deal_rate.append(consultant_data["deal_rate"])
                    avg_price.append(consultant_data["avg_price"])
                    total_performance.append(consultant_data["total_performance"])
                else:
                    # 如果没找到该咨询师，填充0值
                    print(f"未找到咨询师 {consultant_name} 在时间段 {period_start_str}~{period_end_str} 的数据")
                    total_people.append(0)
                    old_customer.append(0)
                    new_first.append(0)
                    new_second.append(0)
                    deal_rate.append(0)
                    avg_price.append(0)
                    total_performance.append(0)
            else:
                # 如果没有指定咨询师，使用汇总数据
                summary = period_data["summary"]
                total_people.append(summary["total_people"])
                deal_rate.append(summary["deal_rate"])
                avg_price.append(summary["avg_price"])
                total_performance.append(summary["total_performance"])

                # 从饼图数据获取客户类型分布
                pie_data = period_data["pie_data"]
                old_val = next((item["value"] for item in pie_data if item["name"] == "老客"), 0)
                new_first_val = next((item["value"] for item in pie_data if item["name"] == "新客首次"), 0)
                new_second_val = next((item["value"] for item in pie_data if item["name"] == "新客二次"), 0)

                old_customer.append(old_val)
                new_first.append(new_first_val)
                new_second.append(new_second_val)
        else:
            # 如果没有数据，填充0值
            mid_date = current_date + timedelta(days=interval_days // 2)
            date_str = mid_date.strftime('%Y-%m-%d')
            dates.append(date_str)

            total_people.append(0)
            old_customer.append(0)
            new_first.append(0)
            new_second.append(0)
            deal_rate.append(0)
            avg_price.append(0)
            total_performance.append(0)

        # 移动到下一个时间点
        current_date += timedelta(days=interval_days)

    return {
        "dates": dates,
        "total_people": total_people,
        "old_customer": old_customer,
        "new_first": new_first,
        "new_second": new_second,
        "deal_rate": deal_rate,
        "avg_price": avg_price,
        "total_performance": total_performance
    }

@app.route('/api/reports')
def list_reports():
    """获取所有报表文件列表"""
    try:
        # 获取当前目录下所有的网电咨询统计Excel文件
        files = []
        for filename in os.listdir('.'):
            if filename.startswith('网电咨询统计_') and filename.endswith('.xlsx'):
                # 获取文件信息
                file_path = os.path.join('.', filename)
                file_stat = os.stat(file_path)
                file_size = file_stat.st_size
                file_mtime = datetime.fromtimestamp(file_stat.st_mtime)

                files.append({
                    'filename': filename,
                    'size': file_size,
                    'size_mb': round(file_size / 1024 / 1024, 2),
                    'modified_time': file_mtime.strftime('%Y-%m-%d %H:%M:%S'),
                    'modified_timestamp': file_stat.st_mtime
                })

        # 按修改时间倒序排列
        files.sort(key=lambda x: x['modified_timestamp'], reverse=True)

        return jsonify({
            'success': True,
            'files': files,
            'count': len(files)
        })

    except Exception as e:
        print(f"获取报表列表失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/download/<filename>')
def download_report(filename):
    """下载指定的报表文件"""
    try:
        # 安全检查：确保文件名符合预期格式
        if not filename.startswith('网电咨询统计_') or not filename.endswith('.xlsx'):
            abort(400, description="无效的文件名")

        # 检查文件是否存在
        file_path = os.path.join('.', filename)
        if not os.path.exists(file_path):
            abort(404, description="文件不存在")

        # 发送文件 (Flask 1.0.2 兼容)
        return send_file(
            file_path,
            as_attachment=True,
            attachment_filename=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

    except Exception as e:
        print(f"下载报表失败: {e}")
        abort(500, description="下载失败")

@app.route('/api/reports/delete/<filename>', methods=['DELETE'])
def delete_report(filename):
    """删除指定的报表文件"""
    try:
        # 安全检查：确保文件名符合预期格式
        if not filename.startswith('网电咨询统计_') or not filename.endswith('.xlsx'):
            return jsonify({'success': False, 'error': '无效的文件名'}), 400

        # 检查文件是否存在
        file_path = os.path.join('.', filename)
        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'}), 404

        # 删除文件
        os.remove(file_path)

        return jsonify({
            'success': True,
            'message': f'文件 {filename} 已成功删除'
        })

    except Exception as e:
        print(f"删除报表失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    import sys
    print("启动网电咨询师统计应用...", flush=True)
    print("访问地址: http://localhost:5004", flush=True)
    print("大屏地址: http://localhost:5004/dashboard", flush=True)
    sys.stdout.flush()
    try:
        app.run(debug=True, host='0.0.0.0', port=5004)
    except Exception as e:
        print(f"应用启动失败: {e}", flush=True)
        import traceback
        traceback.print_exc()
