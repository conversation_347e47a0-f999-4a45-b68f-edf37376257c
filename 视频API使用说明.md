# 视频API使用说明

## 🎉 成功解决403错误！

经过调试和修复，现在可以成功获取视频URL了！

## 📋 最终解决方案

### 1. 主要问题和解决方法

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| 403 Forbidden | CSRF token不匹配 | 使用Cookie中的csrftoken值 |
| JSON解析失败 | 响应被Brotli压缩 | 移除br压缩，只使用gzip |
| file_name错误 | API不允许空值 | 移除file_name参数或使用material_id |

### 2. 关键修复点

1. **CSRF Token同步**：确保x-csrftoken头与Cookie中的csrftoken一致
2. **压缩处理**：修改Accept-Encoding为"gzip, deflate"，避免Brotli压缩
3. **参数优化**：移除或正确设置file_name参数

## 🚀 使用方法

### 基本用法

```python
from get_video_url import get_video_url, get_multiple_videos

# 配置参数（从浏览器获取最新值）
GROUP_ID = "1807160717110459"
MATERIAL_ID = "7531596491141726254"  # 视频素材ID
CSRF_TOKEN = "iqyKE9C6YRt3raqo0F_ThoAo"  # 从Cookie中获取
X_CSRF_TOKEN = "84f2a742f328d5b670ec6f5ed83cefea11755247006"

# 必要的Cookie
cookies = {
    "csrftoken": "iqyKE9C6YRt3raqo0F_ThoAo",
    "sessionid": "1b9a22c0421bd13ec0b5944520a14432",
    "uid_tt": "d9e661fca3791b4e4749c271282c6b8d",
    "sid_tt": "1b9a22c0421bd13ec0b5944520a14432"
}

# 获取特定视频
video_info = get_video_url(
    group_id=GROUP_ID,
    material_id=MATERIAL_ID,
    csrf_token=CSRF_TOKEN,
    x_csrf_token=X_CSRF_TOKEN,
    cookies=cookies
)

if video_info:
    print(f"视频URL: {video_info['video_url']}")
    print(f"视频名称: {video_info['video_name']}")
```

### 获取视频列表

```python
# 获取多个视频
videos_result = get_multiple_videos(
    group_id=GROUP_ID,
    csrf_token=CSRF_TOKEN,
    x_csrf_token=X_CSRF_TOKEN,
    cookies=cookies,
    limit=10  # 获取10个视频
)

if videos_result["success"]:
    for video in videos_result["videos"]:
        print(f"视频: {video['video_name']}")
        print(f"URL: {video['video_url']}")
```

## 📊 测试结果

✅ **成功获取视频信息**：
- 视频ID: v02033g10000d22ps7vog65rh4ndrugg
- 视频名称: 推送视频_0726-13中诺0407.mp4
- 视频URL: https://video-cn-public.bytedance.net/storage/v1/tos-cn-ve-51/...
- 创建时间: 2025-08-02T14:56:30+08:00
- 时长: 56.054秒

✅ **成功获取视频列表**：
- 总数: 3285个视频
- 支持分页获取
- 包含完整的视频信息

## 🔧 获取最新Token的步骤

1. **登录巨量引擎**：https://business.oceanengine.com
2. **进入素材中心** → 视频管理
3. **打开开发者工具**（F12）→ Network标签
4. **刷新页面**，找到video/list请求
5. **复制请求头**中的：
   - `x-csrftoken`: 用于CSRF_TOKEN
   - `x-csrf-token`: 用于X_CSRF_TOKEN
   - `Cookie`: 提取csrftoken、sessionid等

## ⚠️ 注意事项

1. **Token时效性**：Token会过期，需要定期更新
2. **Cookie完整性**：确保包含必要的认证Cookie
3. **请求频率**：避免过于频繁的请求
4. **参数一致性**：确保CSRF token在请求头和Cookie中一致

## 🎯 返回的视频信息

每个视频包含以下信息：
- `video_id`: 视频唯一标识
- `video_url`: **视频播放URL**（主要目标）
- `video_name`: 视频文件名
- `video_poster`: 视频封面图片URL
- `material_id`: 素材ID
- `create_time`: 创建时间
- `duration`: 视频时长（秒）
- `width/height`: 视频尺寸
- `metrics`: 转化指标数据

## 🔄 自动化建议

可以将此功能集成到定时任务中：
1. 定期获取最新的视频列表
2. 下载或处理视频文件
3. 分析视频的转化数据
4. 生成视频效果报告

现在你可以成功获取到video_url了！🎉
