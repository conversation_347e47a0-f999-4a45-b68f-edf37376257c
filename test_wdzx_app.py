#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网电咨询师来院数据统计Web应用 - 测试版本
"""

from flask import Flask, render_template, jsonify
import sys
import os

print("开始导入模块...")

try:
    # 添加当前目录到Python路径
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    print("路径添加成功")

    # 导入数据获取函数
    from 网电咨询师来院统计 import get_all_wdzx_consultants_stats_to_excel
    print("数据获取函数导入成功")

    app = Flask(__name__)
    print("Flask应用创建成功")

    @app.route('/')
    def index():
        """首页"""
        return """
        <html>
        <head><title>网电咨询师统计测试</title></head>
        <body>
            <h1>网电咨询师来院数据统计</h1>
            <p>应用运行正常！</p>
            <p><a href="/dashboard">查看大屏</a></p>
            <p><a href="/test">API测试</a></p>
        </body>
        </html>
        """

    @app.route('/dashboard')
    def dashboard():
        """大屏数据可视化页面"""
        try:
            return render_template('wdzx_dashboard_fullscreen.html',
                                 start_date='2025-09-01',
                                 end_date='2025-09-05')
        except Exception as e:
            return f"模板加载失败: {e}"

    @app.route('/test')
    def test_api():
        """API测试页面"""
        return """
        <html>
        <head><title>API测试</title></head>
        <body>
            <h1>网电咨询师API测试</h1>
            <button onclick="testAPI()">测试API</button>
            <div id="result"></div>
            <script>
                async function testAPI() {
                    const resultDiv = document.getElementById('result');
                    resultDiv.innerHTML = '正在测试...';
                    try {
                        const response = await fetch('/api/test');
                        const data = await response.json();
                        resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    } catch (error) {
                        resultDiv.innerHTML = '错误: ' + error.message;
                    }
                }
            </script>
        </body>
        </html>
        """

    @app.route('/api/test')
    def api_test():
        """测试API"""
        try:
            # 测试调用数据获取函数
            result = {
                "status": "success",
                "message": "API测试成功",
                "function_available": True
            }
            
            # 尝试调用实际函数（但不执行，只检查是否可调用）
            if callable(get_all_wdzx_consultants_stats_to_excel):
                result["data_function"] = "可用"
            else:
                result["data_function"] = "不可用"
                
            return jsonify(result)
        except Exception as e:
            return jsonify({
                "status": "error",
                "message": str(e)
            })

    if __name__ == '__main__':
        print("启动测试应用...")
        print("访问地址: http://localhost:5005")
        print("大屏地址: http://localhost:5005/dashboard")
        try:
            app.run(debug=True, host='0.0.0.0', port=5005)
        except Exception as e:
            print(f"应用启动失败: {e}")
            import traceback
            traceback.print_exc()

except Exception as e:
    print(f"模块导入失败: {e}")
    import traceback
    traceback.print_exc()
