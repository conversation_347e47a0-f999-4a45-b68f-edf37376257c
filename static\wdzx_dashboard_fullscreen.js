// 全局变量
let currentData = null;
let charts = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updateDateTime();
    setInterval(updateDateTime, 1000); // 每秒更新时间
    initCharts();
    loadData();

    // 添加咨询师选择变化事件监听
    const consultantSelect = document.getElementById('consultant-select');
    if (consultantSelect) {
        consultantSelect.addEventListener('change', function() {
            console.log('咨询师选择改变:', this.value);
            loadTrendData(); // 重新加载趋势数据
        });
    }
});

// 更新日期时间显示
function updateDateTime() {
    const now = new Date();
    const dateTimeStr = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    const datetimeElement = document.getElementById('datetime');
    if (datetimeElement) {
        datetimeElement.textContent = dateTimeStr;
    }
}

// 初始化所有图表
function initCharts() {
    // 深色主题配置
    const darkTheme = {
        backgroundColor: 'transparent',
        textStyle: {
            color: '#e2e8f0'
        },
        title: {
            textStyle: {
                color: '#e2e8f0'
            }
        },
        legend: {
            textStyle: {
                color: '#e2e8f0'
            }
        },
        tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#00d4ff',
            textStyle: {
                color: '#ffffff'
            }
        }
    };

    // 初始化图表，添加错误检查
    const chartElements = {
        pieChart: 'pie-chart',
        customerTypeBar: 'customer-type-bar',
        performanceBar: 'performance-bar',
        dealCountBar: 'deal-count-bar',
        dealRateBar: 'deal-rate-bar',
        avgPriceBar: 'avg-price-bar',
        performanceLine: 'performance-line',
        peopleTrend: 'people-trend',
        customerTrend: 'customer-trend',
        dealRateTrend: 'deal-rate-trend',
        avgPriceTrend: 'avg-price-trend'
    };

    for (const [chartName, elementId] of Object.entries(chartElements)) {
        const element = document.getElementById(elementId);
        if (element) {
            charts[chartName] = echarts.init(element, 'dark');
            console.log(`图表 ${chartName} 初始化成功，元素ID: ${elementId}`);
        } else {
            console.error(`找不到图表元素: ${elementId}`);
        }
    }

    console.log('所有图表初始化完成，charts对象:', Object.keys(charts));

    // 窗口大小改变时重新调整图表
    window.addEventListener('resize', function() {
        Object.values(charts).forEach(chart => chart.resize());
    });
}

// 加载数据
async function loadData() {
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;

    if (!startDate || !endDate) {
        alert('请选择开始日期和结束日期');
        return;
    }

    console.log('开始加载数据:', { startDate, endDate });

    try {
        // 显示加载状态
        showLoadingState();

        const response = await fetch(`/api/stats?start_date=${startDate}&end_date=${endDate}`);
        const data = await response.json();

        console.log('API响应:', data);

        if (!response.ok) {
            throw new Error(data.error || '获取数据失败');
        }

        currentData = data;
        updateSummaryCards(data.summary);
        updateConsultantSelect(data.consultants);
        renderCharts(data);
        
        // 加载趋势数据
        await loadTrendData();

        console.log('数据加载完成');
    } catch (error) {
        console.error('加载数据失败:', error);
        showErrorState(error.message);
    }
}

// 显示加载状态
function showLoadingState() {
    // 清空汇总卡片
    document.getElementById('total-performance').textContent = '加载中...';
    document.getElementById('total-people').textContent = '加载中...';
    document.getElementById('total-deal').textContent = '加载中...';
    document.getElementById('deal-rate').textContent = '加载中...';
    document.getElementById('avg-price').textContent = '加载中...';

    // 清空图表
    Object.values(charts).forEach(chart => {
        chart.clear();
        chart.showLoading();
    });
}

// 显示错误状态
function showErrorState(message) {
    // 更新汇总卡片
    document.getElementById('total-performance').textContent = '错误';
    document.getElementById('total-people').textContent = '错误';
    document.getElementById('total-deal').textContent = '错误';
    document.getElementById('deal-rate').textContent = '错误';
    document.getElementById('avg-price').textContent = '错误';

    // 隐藏图表加载状态
    Object.values(charts).forEach(chart => {
        chart.hideLoading();
        chart.clear();
    });

    alert(`加载数据失败: ${message}`);
}

// 更新汇总卡片
function updateSummaryCards(summary) {
    document.getElementById('total-performance').textContent = summary.total_performance.toLocaleString();
    document.getElementById('total-people').textContent = summary.total_people.toLocaleString();
    document.getElementById('total-deal').textContent = summary.total_deal.toLocaleString();
    document.getElementById('deal-rate').textContent = summary.deal_rate + '%';
    document.getElementById('avg-price').textContent = summary.avg_price.toLocaleString();
}

// 更新咨询师下拉选择框
function updateConsultantSelect(consultants) {
    const select = document.getElementById('consultant-select');
    if (!select) return;

    // 清空现有选项（保留第一个默认选项）
    select.innerHTML = '<option value="">请选择咨询师</option>';

    // 添加咨询师选项
    consultants.forEach(consultant => {
        const option = document.createElement('option');
        option.value = consultant.name;
        option.textContent = consultant.name;
        select.appendChild(option);
    });

    console.log('咨询师选择框更新完成，共', consultants.length, '个咨询师');
}

// 渲染所有图表
function renderCharts(data) {
    console.log('开始渲染图表，数据:', data);

    // 隐藏所有图表的加载状态
    Object.values(charts).forEach(chart => chart.hideLoading());

    // 渲染饼图
    renderPieChart(data.pie_data);

    // 渲染柱状图
    renderBarCharts(data.bar_data);

    console.log('所有图表渲染完成');
}

// 渲染饼图
function renderPieChart(pieData) {
    if (!charts.pieChart) {
        console.error('饼图未初始化');
        return;
    }

    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'vertical',
            left: 'left',
            textStyle: {
                color: '#e2e8f0'
            }
        },
        series: [
            {
                name: '客户类型',
                type: 'pie',
                radius: '50%',
                data: pieData,
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                },
                itemStyle: {
                    borderRadius: 8,
                    borderColor: '#fff',
                    borderWidth: 2
                }
            }
        ]
    };

    charts.pieChart.setOption(option);
    console.log('饼图渲染完成');
}

// 渲染柱状图
function renderBarCharts(barData) {
    // 客户类型堆叠柱状图
    if (charts.customerTypeBar) {
        const customerTypeOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            legend: {
                data: ['老客', '新客首次', '新客二次'],
                textStyle: {
                    color: '#e2e8f0'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: barData.names,
                axisLabel: {
                    color: '#e2e8f0',
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#e2e8f0'
                }
            },
            series: [
                {
                    name: '老客',
                    type: 'bar',
                    stack: '总量',
                    data: barData.old_customer,
                    itemStyle: {
                        color: '#ff6b6b'
                    }
                },
                {
                    name: '新客首次',
                    type: 'bar',
                    stack: '总量',
                    data: barData.new_first,
                    itemStyle: {
                        color: '#4ecdc4'
                    }
                },
                {
                    name: '新客二次',
                    type: 'bar',
                    stack: '总量',
                    data: barData.new_second,
                    itemStyle: {
                        color: '#45b7d1'
                    }
                }
            ]
        };
        charts.customerTypeBar.setOption(customerTypeOption);
    }

    // 总开单业绩柱状图
    if (charts.performanceBar) {
        const performanceOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: barData.names,
                axisLabel: {
                    color: '#e2e8f0',
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#e2e8f0'
                }
            },
            series: [
                {
                    name: '总开单业绩',
                    type: 'bar',
                    data: barData.performance,
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#00d4ff' },
                            { offset: 1, color: '#0099cc' }
                        ])
                    }
                }
            ]
        };
        charts.performanceBar.setOption(performanceOption);
    }

    // 成交人数柱状图
    if (charts.dealCountBar) {
        const dealCountOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: barData.names,
                axisLabel: {
                    color: '#e2e8f0',
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#e2e8f0'
                }
            },
            series: [
                {
                    name: '成交人数',
                    type: 'bar',
                    data: barData.deal_count,
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#00ff88' },
                            { offset: 1, color: '#00cc66' }
                        ])
                    }
                }
            ]
        };
        charts.dealCountBar.setOption(dealCountOption);
    }

    // 成交率柱状图
    if (charts.dealRateBar) {
        const dealRateOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: function(params) {
                    return params[0].name + '<br/>' +
                           params[0].seriesName + ': ' + params[0].value + '%';
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: barData.names,
                axisLabel: {
                    color: '#e2e8f0',
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#e2e8f0',
                    formatter: '{value}%'
                }
            },
            series: [
                {
                    name: '成交率',
                    type: 'bar',
                    data: barData.deal_rate,
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#ffd700' },
                            { offset: 1, color: '#ffb300' }
                        ])
                    }
                }
            ]
        };
        charts.dealRateBar.setOption(dealRateOption);
    }

    // 客单价柱状图
    if (charts.avgPriceBar) {
        const avgPriceOption = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: barData.names,
                axisLabel: {
                    color: '#e2e8f0',
                    rotate: 45
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#e2e8f0'
                }
            },
            series: [
                {
                    name: '客单价',
                    type: 'bar',
                    data: barData.avg_price,
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: '#ff6b6b' },
                            { offset: 1, color: '#ee5a52' }
                        ])
                    }
                }
            ]
        };
        charts.avgPriceBar.setOption(avgPriceOption);
    }

    console.log('柱状图渲染完成');
}

// 加载趋势数据
async function loadTrendData() {
    const startDate = document.getElementById('start-date').value;
    const endDate = document.getElementById('end-date').value;
    const intervalDays = document.getElementById('interval-days').value || 7;
    const consultantName = document.getElementById('consultant-select').value;

    if (!startDate || !endDate) {
        console.log('日期参数不完整，跳过趋势数据加载');
        return;
    }

    console.log('开始加载趋势数据:', { startDate, endDate, intervalDays, consultantName });

    try {
        const url = `/api/consultant_trend?start_date=${startDate}&end_date=${endDate}&interval_days=${intervalDays}${consultantName ? '&consultant_name=' + encodeURIComponent(consultantName) : ''}`;
        console.log('趋势数据API URL:', url);

        const response = await fetch(url);
        const trendData = await response.json();

        console.log('趋势数据API响应:', trendData);

        if (!response.ok) {
            throw new Error(trendData.error || '获取趋势数据失败');
        }

        renderTrendCharts(trendData);
        renderPerformanceLine(trendData);

        console.log('趋势数据加载完成');
    } catch (error) {
        console.error('加载趋势数据失败:', error);
    }
}

// 渲染趋势图表
function renderTrendCharts(trendData) {
    // 总人数趋势
    if (charts.peopleTrend) {
        const peopleTrendOption = {
            tooltip: {
                trigger: 'axis'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: trendData.dates,
                axisLabel: {
                    color: '#e2e8f0'
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#e2e8f0'
                }
            },
            series: [
                {
                    name: '总人数',
                    type: 'line',
                    data: trendData.total_people,
                    smooth: true,
                    itemStyle: {
                        color: '#00d4ff'
                    },
                    lineStyle: {
                        color: '#00d4ff'
                    }
                }
            ]
        };
        charts.peopleTrend.setOption(peopleTrendOption);
    }

    // 客户类型趋势
    if (charts.customerTrend) {
        const customerTrendOption = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['老客', '新客首次', '新客二次'],
                textStyle: {
                    color: '#e2e8f0'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: trendData.dates,
                axisLabel: {
                    color: '#e2e8f0'
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#e2e8f0'
                }
            },
            series: [
                {
                    name: '老客',
                    type: 'line',
                    data: trendData.old_customer,
                    smooth: true,
                    itemStyle: {
                        color: '#ff6b6b'
                    },
                    lineStyle: {
                        color: '#ff6b6b'
                    }
                },
                {
                    name: '新客首次',
                    type: 'line',
                    data: trendData.new_first,
                    smooth: true,
                    itemStyle: {
                        color: '#4ecdc4'
                    },
                    lineStyle: {
                        color: '#4ecdc4'
                    }
                },
                {
                    name: '新客二次',
                    type: 'line',
                    data: trendData.new_second,
                    smooth: true,
                    itemStyle: {
                        color: '#45b7d1'
                    },
                    lineStyle: {
                        color: '#45b7d1'
                    }
                }
            ]
        };
        charts.customerTrend.setOption(customerTrendOption);
    }

    // 成交率趋势
    if (charts.dealRateTrend) {
        const dealRateTrendOption = {
            tooltip: {
                trigger: 'axis',
                formatter: function(params) {
                    return params[0].name + '<br/>' +
                           params[0].seriesName + ': ' + params[0].value + '%';
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: trendData.dates,
                axisLabel: {
                    color: '#e2e8f0'
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#e2e8f0',
                    formatter: '{value}%'
                }
            },
            series: [
                {
                    name: '成交率',
                    type: 'line',
                    data: trendData.deal_rate,
                    smooth: true,
                    itemStyle: {
                        color: '#ffd700'
                    },
                    lineStyle: {
                        color: '#ffd700'
                    }
                }
            ]
        };
        charts.dealRateTrend.setOption(dealRateTrendOption);
    }

    // 客单价趋势
    if (charts.avgPriceTrend) {
        const avgPriceTrendOption = {
            tooltip: {
                trigger: 'axis'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: trendData.dates,
                axisLabel: {
                    color: '#e2e8f0'
                }
            },
            yAxis: {
                type: 'value',
                axisLabel: {
                    color: '#e2e8f0'
                }
            },
            series: [
                {
                    name: '客单价',
                    type: 'line',
                    data: trendData.avg_price,
                    smooth: true,
                    itemStyle: {
                        color: '#ff6b6b'
                    },
                    lineStyle: {
                        color: '#ff6b6b'
                    }
                }
            ]
        };
        charts.avgPriceTrend.setOption(avgPriceTrendOption);
    }

    console.log('趋势图表渲染完成');
}

// 渲染业绩折线图
function renderPerformanceLine(trendData) {
    if (!charts.performanceLine) {
        console.error('业绩折线图未初始化');
        return;
    }

    const performanceLineOption = {
        tooltip: {
            trigger: 'axis'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: trendData.dates,
            axisLabel: {
                color: '#e2e8f0'
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#e2e8f0'
            }
        },
        series: [
            {
                name: '总开单业绩',
                type: 'line',
                data: trendData.total_performance,
                smooth: true,
                itemStyle: {
                    color: '#00ff88'
                },
                lineStyle: {
                    color: '#00ff88',
                    width: 3
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(0, 255, 136, 0.3)' },
                        { offset: 1, color: 'rgba(0, 255, 136, 0.1)' }
                    ])
                }
            }
        ]
    };

    charts.performanceLine.setOption(performanceLineOption);
    console.log('业绩折线图渲染完成');
}

// 报表管理功能
function showReportManager() {
    const modal = document.getElementById('reportModal');
    if (modal) {
        modal.classList.add('show');
        refreshReportList();
    }
}

function closeReportManager() {
    const modal = document.getElementById('reportModal');
    if (modal) {
        modal.classList.remove('show');
    }
}

async function refreshReportList() {
    const reportList = document.getElementById('reportList');
    if (!reportList) return;

    reportList.innerHTML = '<div class="loading">正在加载报表列表...</div>';

    try {
        const response = await fetch('/api/reports');
        const data = await response.json();

        if (data.success && data.files && data.files.length > 0) {
            let html = '';
            data.files.forEach(file => {
                html += `
                    <div class="report-item">
                        <div class="report-info">
                            <div class="report-name">${file.filename}</div>
                            <div class="report-details">
                                <span class="detail-item">${file.size_mb} MB</span>
                                <span class="detail-item">${file.modified_time}</span>
                            </div>
                        </div>
                        <div class="report-actions">
                            <button class="download-btn" onclick="downloadReport('${file.filename}')">下载</button>
                            <button class="delete-btn" onclick="deleteReport('${file.filename}')">删除</button>
                        </div>
                    </div>
                `;
            });
            reportList.innerHTML = html;
        } else {
            reportList.innerHTML = '<div class="no-reports">暂无报表文件</div>';
        }
    } catch (error) {
        console.error('获取报表列表失败:', error);
        reportList.innerHTML = '<div class="error">获取报表列表失败</div>';
    }
}

function downloadReport(filename) {
    window.open(`/api/reports/download/${encodeURIComponent(filename)}`, '_blank');
}

async function deleteReport(filename) {
    if (!confirm(`确定要删除报表文件 "${filename}" 吗？`)) {
        return;
    }

    try {
        const response = await fetch(`/api/reports/delete/${encodeURIComponent(filename)}`, {
            method: 'DELETE'
        });
        const data = await response.json();

        if (data.success) {
            alert('删除成功');
            refreshReportList();
        } else {
            alert('删除失败: ' + data.error);
        }
    } catch (error) {
        console.error('删除报表失败:', error);
        alert('删除失败: ' + error.message);
    }
}

// 点击模态框外部关闭
document.addEventListener('click', function(event) {
    const modal = document.getElementById('reportModal');
    if (event.target === modal) {
        closeReportManager();
    }
});

// 键盘ESC关闭模态框
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeReportManager();
    }
});
