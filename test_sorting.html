<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>柱状图排序测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #666;
            margin-top: 0;
        }
        .data-display {
            background: #f9f9f9;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 柱状图排序功能测试</h1>
        
        <div class="test-section">
            <h3>1. 排序函数测试</h3>
            <div id="sort-test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 模拟数据测试</h3>
            <div id="data-test-result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. 5个柱状图排序验证</h3>
            <div id="charts-test-result"></div>
        </div>
    </div>

    <script>
        // 复制排序函数
        function sortDataByValue(names, values, ...otherArrays) {
            const indices = Array.from({length: names.length}, (_, i) => i);
            indices.sort((a, b) => values[b] - values[a]); // 降序排序
            
            const sortedNames = indices.map(i => names[i]);
            const sortedValues = indices.map(i => values[i]);
            const sortedOthers = otherArrays.map(arr => indices.map(i => arr[i]));
            
            return [sortedNames, sortedValues, ...sortedOthers];
        }

        // 测试1：排序函数基本功能
        function testSortFunction() {
            const names = ['张三', '李四', '王五'];
            const values = [100, 300, 200];
            
            const [sortedNames, sortedValues] = sortDataByValue(names, values);
            
            const expected = ['李四', '王五', '张三'];
            const expectedValues = [300, 200, 100];
            
            const isCorrect = JSON.stringify(sortedNames) === JSON.stringify(expected) &&
                             JSON.stringify(sortedValues) === JSON.stringify(expectedValues);
            
            document.getElementById('sort-test-result').innerHTML = `
                <div class="data-display">
                    原始数据: ${JSON.stringify({names, values})}
                    <br>排序结果: ${JSON.stringify({names: sortedNames, values: sortedValues})}
                    <br>期望结果: ${JSON.stringify({names: expected, values: expectedValues})}
                </div>
                <p class="${isCorrect ? 'success' : 'error'}">
                    ${isCorrect ? '✓ 排序函数测试通过' : '✗ 排序函数测试失败'}
                </p>
            `;
        }

        // 测试2：模拟网电咨询师数据
        function testWithMockData() {
            const mockData = {
                names: ['刘莹（网络）', '吴众霞（网络）', '张晶晶（网络）', '徐帆', '朱丽娟（网络）'],
                performance: [150000, 200000, 100000, 180000, 120000],
                deal_count: [15, 20, 10, 18, 12],
                deal_rate: [25.0, 30.0, 20.0, 28.0, 22.0],
                avg_price: [10000, 10000, 10000, 10000, 10000],
                old_customer: [8, 12, 6, 10, 7],
                new_first: [5, 6, 3, 6, 4],
                new_second: [2, 2, 1, 2, 1]
            };

            // 测试各种排序
            const tests = [
                {
                    name: '按总开单业绩排序',
                    sortBy: 'performance',
                    expected: ['吴众霞（网络）', '徐帆', '刘莹（网络）', '朱丽娟（网络）', '张晶晶（网络）']
                },
                {
                    name: '按成交人数排序',
                    sortBy: 'deal_count',
                    expected: ['吴众霞（网络）', '徐帆', '刘莹（网络）', '朱丽娟（网络）', '张晶晶（网络）']
                },
                {
                    name: '按成交率排序',
                    sortBy: 'deal_rate',
                    expected: ['吴众霞（网络）', '徐帆', '刘莹（网络）', '朱丽娟（网络）', '张晶晶（网络）']
                }
            ];

            let results = '';
            tests.forEach(test => {
                const [sortedNames] = sortDataByValue(mockData.names, mockData[test.sortBy]);
                const isCorrect = JSON.stringify(sortedNames) === JSON.stringify(test.expected);
                
                results += `
                    <div class="data-display">
                        ${test.name}: ${JSON.stringify(sortedNames)}
                        <br>期望结果: ${JSON.stringify(test.expected)}
                    </div>
                    <p class="${isCorrect ? 'success' : 'error'}">
                        ${isCorrect ? '✓' : '✗'} ${test.name}
                    </p>
                `;
            });

            document.getElementById('data-test-result').innerHTML = results;
        }

        // 测试3：验证5个柱状图的排序逻辑
        function testChartsLogic() {
            const mockBarData = {
                names: ['A咨询师', 'B咨询师', 'C咨询师'],
                performance: [100, 300, 200],
                deal_count: [10, 30, 20],
                deal_rate: [20, 40, 30],
                avg_price: [5000, 8000, 6000],
                old_customer: [5, 15, 10],
                new_first: [3, 10, 8],
                new_second: [2, 5, 2]
            };

            const charts = [
                {
                    name: '客户类型堆叠柱状图（按总客户数）',
                    logic: () => {
                        const totalCustomers = mockBarData.old_customer.map((old, i) => 
                            old + mockBarData.new_first[i] + mockBarData.new_second[i]
                        );
                        return sortDataByValue(mockBarData.names, totalCustomers);
                    }
                },
                {
                    name: '总开单业绩柱状图',
                    logic: () => sortDataByValue(mockBarData.names, mockBarData.performance)
                },
                {
                    name: '成交人数柱状图',
                    logic: () => sortDataByValue(mockBarData.names, mockBarData.deal_count)
                },
                {
                    name: '成交率柱状图',
                    logic: () => sortDataByValue(mockBarData.names, mockBarData.deal_rate)
                },
                {
                    name: '客单价柱状图',
                    logic: () => sortDataByValue(mockBarData.names, mockBarData.avg_price)
                }
            ];

            let results = '';
            charts.forEach((chart, index) => {
                try {
                    const [sortedNames] = chart.logic();
                    results += `
                        <div class="data-display">
                            ${index + 1}. ${chart.name}
                            <br>排序结果: ${JSON.stringify(sortedNames)}
                        </div>
                        <p class="success">✓ ${chart.name} 排序逻辑正常</p>
                    `;
                } catch (error) {
                    results += `
                        <p class="error">✗ ${chart.name} 排序逻辑错误: ${error.message}</p>
                    `;
                }
            });

            document.getElementById('charts-test-result').innerHTML = results;
        }

        // 运行所有测试
        document.addEventListener('DOMContentLoaded', function() {
            testSortFunction();
            testWithMockData();
            testChartsLogic();
        });
    </script>
</body>
</html>
