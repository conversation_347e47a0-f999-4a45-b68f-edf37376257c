import requests


headers = {
    "sec-ch-ua-platform": "Windows",
    "x-csrftoken": "Z6yunS9339c9Xx1Z5DY8KUfK",
    "x-csrf-token": "84f2a742f328d5b670ec6f5ed83cefea11755247006",
    "Referer": "https://business.oceanengine.com/site/asset/material_center/management/video?cc_id=1807160717110459",
    "sec-ch-ua": "Not)A;Brand;v=8, Chromium;v=138, Google",
    "sec-ch-ua-mobile": "?0",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "Accept": "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "csrf_token":"e78923279a95857344ed1092d694235b11755249077"

}
url = "https://business.oceanengine.com/nbs/api/bm/video/list"
params = {
    "group_id": "1807160717110459^"
}
data = {
    "group_id": "1807160717110459",
    "version": "v2",
    "material_platform": 49,
    "tags": [],
    "image_mode_filter": [],
    "material_property_filter": [],
    "sources": [],
    "order_by": "create_time",
    "is_asc": False,
    "file_name": "7531596491141726254",
    "include_metrics": ["convert_cnt", "conversion_cost", "conversion_rate"],
    "metrics_start_time": "2025-08-08"
}
response = requests.post(url, headers=headers, params=params, json=data)

print(response.text)
print(response)