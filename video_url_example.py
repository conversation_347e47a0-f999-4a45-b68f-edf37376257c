#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
巨量引擎视频URL获取示例
演示如何使用get_video_url.py中的功能获取完整的视频信息
"""

from get_video_url import (
    parse_cookie_string,
    get_complete_video_info,
    get_video_url_by_id,
    get_video_info_by_id
)


def main():
    """
    主函数：演示视频URL获取的完整流程
    """
    print("=== 巨量引擎视频URL获取示例 ===\n")
    
    # 配置信息（需要根据实际情况修改）
    group_id = "1807160717110459"  # 替换为实际的组ID
    material_id = "your_material_id"  # 替换为实际的素材ID
    
    # 从您提供的cURL命令中提取的Cookie和Token
    cookie_string = """ttcid=a4037d82677d42d782bddc5a2abfe70217; gr_user_id=d4c9e21c-8ecc-4f21-8a05-9d74c5d2348e; grwng_uid=f29c2a4f-**************-9cdb6bb0b35e; store-region=cn-ah; store-region-src=uid; is_hit_partitioned_cookie_canary_ss=true; is_hit_partitioned_cookie_canary_ss=true; is_staff_user=false; is_hit_partitioned_cookie_canary=true; is_hit_partitioned_cookie_canary=true; n_mh=we4sdJUEQEJR2yFJVWSKhZ2TUDg4P82QmFtCQsF_ze0; tt_scid=EsuDluvolF.lEn32DlEfDDQ7AGMARwRoAKZUfXZzxwYxlAUQqo7Ba.F8IVwHZOGO98b1; passport_csrf_token=cdf96cd64b0960a8c55788e19295e12f; passport_csrf_token_default=cdf96cd64b0960a8c55788e19295e12f; passport_mfa_token=CjeeDV7gHtYeXEq50uxjUNE^%^2FZCXYmuGkL3T6Xurov4sHkbhurORjG5PXBvpqZo6gz8poK64kRO^%^2FvGkoKPAAAAAAAAAAAAABPMQE55VQEULQtuKzM2M1EshQBaL8^%^2FJ2pmgXQg5CaXfGT2O8EIA8crJkb6qb2OjTmhshCz6PUNGPax0WwgAiIBA9LvtbY^%^3D; d_ticket=9ed830468ab37cea9d04fd0626a7057430095; sso_uid_tt=2ce26173525f16c27850a8a16cb70125; sso_uid_tt_ss=2ce26173525f16c27850a8a16cb70125; toutiao_sso_user=1b94430137b7c38c79bd5ac1b2a785a7; toutiao_sso_user_ss=1b94430137b7c38c79bd5ac1b2a785a7; sid_ucp_sso_v1=1.0.0-KGYyMzE5YjYwMGUyNmMyZjNlZjIwOTU0ZGQ3MTc3MzcxM2IyZjdmMjYKHwizi_Cugs2dBRDo-pzDBhj6CiAMMJO8ibQGOAJA8QcaAmxmIiAxYjk0NDMwMTM3YjdjMzhjNzliZDVhYzFiMmE3ODVhNw; ssid_ucp_sso_v1=1.0.0-KGYyMzE5YjYwMGUyNmMyZjNlZjIwOTU0ZGQ3MTc3MzcxM2IyZjdmMjYKHwizi_Cugs2dBRDo-pzDBhj6CiAMMJO8ibQGOAJA8QcaAmxmIiAxYjk0NDMwMTM3YjdjMzhjNzliZDVhYzFiMmE3ODVhNw; uid_tt=d9e661fca3791b4e4749c271282c6b8d; uid_tt_ss=d9e661fca3791b4e4749c271282c6b8d; uid_tt_ss=d9e661fca3791b4e4749c271282c6b8d; sid_tt=1b9a22c0421bd13ec0b5944520a14432; sessionid=1b9a22c0421bd13ec0b5944520a14432; sessionid_ss=1b9a22c0421bd13ec0b5944520a14432; sessionid_ss=1b9a22c0421bd13ec0b5944520a14432; sid_ucp_v1=1.0.0-KDMwZjhkNjEyZmQzODFmMzRhZWQ1NjQ4ODk1NjBiYWY1OTFhN2E2ZmIKGQizi_Cugs2dBRDo-pzDBhj6CiAMOAJA8QcaAmxmIiAxYjlhMjJjMDQyMWJkMTNlYzBiNTk0NDUyMGExNDQzMg; ssid_ucp_v1=1.0.0-KDMwZjhkNjEyZmQzODFmMzRhZWQ1NjQ4ODk1NjBiYWY1OTFhN2E2ZmIKGQizi_Cugs2dBRDo-pzDBhj6CiAMOAJA8QcaAmxmIiAxYjlhMjJjMDQyMWJkMTNlYzBiNTk0NDUyMGExNDQzMg; odin_tt=3af133251b4066689b37ff801c871259543835622173ccf3d9be54585e815a475e2b1e708778e68f31c2a8f66b1942218166aefe8c588846f88655852f78ed38; sid_guard=1b9a22c0421bd13ec0b5944520a14432^%^7C1751934458^%^7C5184000^%^7CSat^%^2C+06-Sep-2025+00^%^3A27^%^3A38+GMT; sid_ucp_v1=1.0.0-KDE1NjE1NmUzOTQ3M2Q3YjgzOTUwMmRlY2U5NjdkZWU5ODI4YWM1MzYKGQizi_Cugs2dBRD6y7HDBhj6CiAMOAJA8QcaAmxmIiAxYjlhMjJjMDQyMWJkMTNlYzBiNTk0NDUyMGExNDQzMg; ssid_ucp_v1=1.0.0-KDE1NjE1NmUzOTQ3M2Q3YjgzOTUwMmRlY2U5NjdkZWU5ODI4YWM1MzYKGQizi_Cugs2dBRD6y7HDBhj6CiAMOAJA8QcaAmxmIiAxYjlhMjJjMDQyMWJkMTNlYzBiNTk0NDUyMGExNDQzMg; a7b76a7621d895df_gr_last_sent_cs1=1827622897523092; a7b76a7621d895df_gr_cs1=1827622897523092; session_tlb_tag=sttt^%^7C8^%^7CG5oiwEIb0T7AtZRFIKFEMv_________LJ1996br1u0wh8D4H829RZ0I7yWiLm2LcSqCxEu6MbgE^%^3D; session_tlb_tag=sttt^%^7C8^%^7CG5oiwEIb0T7AtZRFIKFEMv_________LJ1996br1u0wh8D4H829RZ0I7yWiLm2LcSqCxEu6MbgE^%^3D; aefa4e5d2593305f_gr_last_sent_cs1=1806720071681024; aefa4e5d2593305f_gr_cs1=1806720071681024; ttwid=1^%^7ChJTaYfvaIDaDkop7Tptyv1EV2Cab28wCNVAvrOxu8gs^%^7C1754292970^%^7Cbb18cb7fea1bbb80028faeb834620b98ba9add5c602075f2a5163c192fcafe31; s_v_web_id=verify_mdz8mag8_Ern5C7ZB_4xPK_4Q0C_98h0_MqGUOAmRVn6o; csrftoken=YxswAO5XaPtoAFNBSItaJB0Q; csrf_session_id=01516454031d4b94ed64d3b35c6cfc57; trace_log_adv_id=; trace_log_user_id=2944939450959283"""
    
    # Token信息
    csrf_token = "4602caf96aa321248fbba9fdbba931ca11755595411"
    x_csrf_token = "YxswAO5XaPtoAFNBSItaJB0Q"
    
    # 解析Cookie
    cookies = parse_cookie_string(cookie_string)
    
    print("📋 配置信息:")
    print(f"   组ID: {group_id}")
    print(f"   素材ID: {material_id}")
    print(f"   CSRF Token: {csrf_token[:20]}...")
    print(f"   X-CSRF Token: {x_csrf_token}")
    print(f"   Cookie数量: {len(cookies)} 个")
    
    # 示例1：如果您已经知道视频ID，可以直接获取详细信息
    print("\n" + "="*50)
    print("📹 示例1：通过已知视频ID获取详细信息")
    print("="*50)
    
    # 使用您提供的示例视频ID
    test_video_id = "v02033g10000d22ps7vog65rh4ndrugg"
    
    print(f"🔍 正在获取视频ID: {test_video_id} 的详细信息...")
    
    detailed_info = get_video_url_by_id(test_video_id, csrf_token, x_csrf_token, cookies)
    
    if detailed_info:
        print("✅ 获取成功！视频详细信息:")
        print(f"   📺 视频ID: {detailed_info.get('video_id')}")
        print(f"   🔗 视频URL: {detailed_info.get('video_url')}")
        print(f"   🎬 原始视频URL: {detailed_info.get('original_video_url')}")
        print(f"   🖼️  封面URL: {detailed_info.get('cover_url')}")
        print(f"   📏 视频尺寸: {detailed_info.get('vwidth')}x{detailed_info.get('vheight')}")
        print(f"   ⏱️  视频时长: {detailed_info.get('video_duration')} 秒")
        print(f"   💾 文件大小: {detailed_info.get('video_size')} bytes ({detailed_info.get('video_size', 0) / 1024 / 1024:.2f} MB)")
        print(f"   🎵 比特率: {detailed_info.get('bitrate')}")
        print(f"   📄 格式: {detailed_info.get('format')}")
        print(f"   🔐 文件哈希: {detailed_info.get('file_hash')}")
        print(f"   📊 状态: {detailed_info.get('status')}")
        
        # 提取video_url
        video_url = detailed_info.get('video_url')
        if video_url:
            print(f"\n🎯 提取的video_url:")
            print(f"   {video_url}")
        
    else:
        print("❌ 获取失败！请检查:")
        print("   1. 视频ID是否正确")
        print("   2. Cookie和Token是否有效")
        print("   3. 网络连接是否正常")
    
    # 示例2：完整流程（先获取视频ID，再获取详细信息）
    print("\n" + "="*50)
    print("🔄 示例2：完整流程（需要有效的素材ID）")
    print("="*50)
    
    print("⚠️  注意：此示例需要有效的素材ID，当前使用的是占位符")
    print("   如果您有真实的素材ID，请替换 material_id 变量")
    
    # 这里可以尝试完整流程，但需要有效的material_id
    # complete_info = get_complete_video_info(group_id, material_id, csrf_token, x_csrf_token, cookies)
    
    # 示例3：批量获取多个视频信息
    print("\n" + "="*50)
    print("📦 示例3：批量获取多个视频信息")
    print("="*50)
    
    # 示例视频ID列表
    video_ids = [
        "v02033g10000d22ps7vog65rh4ndrugg",
        # 可以添加更多视频ID
    ]
    
    print(f"🔍 正在批量获取 {len(video_ids)} 个视频的信息...")
    
    batch_info = get_video_info_by_id(video_ids, csrf_token, x_csrf_token, cookies)
    
    if batch_info:
        print("✅ 批量获取成功！")
        for video_id, info in batch_info.items():
            print(f"\n📹 视频: {video_id}")
            print(f"   🔗 URL: {info.get('video_url', 'N/A')}")
            print(f"   📏 尺寸: {info.get('vwidth')}x{info.get('vheight')}")
            print(f"   ⏱️  时长: {info.get('video_duration')} 秒")
    else:
        print("❌ 批量获取失败！")
    
    print("\n" + "="*50)
    print("🎉 示例演示完成！")
    print("="*50)
    print("💡 使用提示:")
    print("   1. 确保Cookie和Token是最新的（从浏览器复制）")
    print("   2. 视频ID格式通常为: v02033g10000xxxxxxxxxx")
    print("   3. 如果获取失败，检查网络和认证信息")
    print("   4. video_url可以直接用于视频播放或下载")


if __name__ == "__main__":
    main()
