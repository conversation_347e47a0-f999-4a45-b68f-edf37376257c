// 格式化数字
function formatNumber(num) {
    return num.toLocaleString('zh-CN');
}

// 格式化金额
function formatAmount(amount) {
    return '¥' + amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 });
}

// 格式化投产比
function formatROI(roi) {
    return roi.toFixed(2);
}

// 获取投产比的颜色类名
function getROIColorClass(roi) {
    if (roi >= 3) return 'roi-good';
    if (roi >= 1) return 'roi-normal';
    return 'roi-bad';
}

// 初始化图表
function initChart(elementId, title, data) {
    const container = document.getElementById(elementId);
    if (!container) {
        console.error(`找不到元素: ${elementId}`);
        return;
    }

    const chart = echarts.init(container);
    const option = {
        title: {
            text: title,
            left: 'center'
        },
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                if (params.name.includes('金额')) {
                    return `${params.name}: ${formatAmount(params.value)}<br/>占比: ${params.percent}%`;
                } else {
                    return `${params.name}: ${formatNumber(params.value)}人<br/>占比: ${params.percent}%`;
                }
            }
        },
        legend: {
            orient: 'horizontal',
            bottom: 10,
            formatter: function(name) {
                const item = data.find(d => d.name === name);
                if (!item) return name;
                
                const total = data.reduce((sum, d) => sum + d.value, 0);
                const percentage = ((item.value / total) * 100).toFixed(1);
                return `${name} ${percentage}%`;
            }
        },
        series: [
            {
                type: 'pie',
                radius: '50%',
                data: data,
                label: {
                    show: true,
                    formatter: '{b}\n{d}%',
                    position: 'outside',
                    alignTo: 'edge',
                    minMargin: 5,
                    edgeDistance: 10,
                    lineHeight: 15
                },
                labelLine: {
                    length: 15,
                    length2: 0,
                    maxSurfaceAngle: 80
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    };
    
    chart.setOption(option);
    
    window.addEventListener('resize', () => {
        chart.resize();
    });
}

// 初始化柱状图
function initBarChart(elementId, title, data, legendNames, dataKeys, yAxisName, isAmount = false, isStack = false) {
    const container = document.getElementById(elementId);
    if (!container) {
        console.error(`找不到元素: ${elementId}`);
        return;
    }

    const chart = echarts.init(container);
    const option = {
        title: {
            text: title,
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function(params) {
                const name = params[0].name;
                let result = `${name}<br/>`;
                
                params.forEach(param => {
                    const value = isAmount ? formatAmount(param.value) : formatNumber(param.value);
                    const unit = isAmount ? '' : '人';
                    result += `${param.seriesName}：${value}${unit}<br/>`;
                });
                
                return result;
            }
        },
        legend: {
            data: legendNames,
            top: 30
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: data.names,
            axisLabel: {
                interval: 0,
                rotate: 30
            }
        },
        yAxis: {
            type: 'value',
            name: yAxisName
        },
        series: dataKeys.map((key, index) => ({
            name: legendNames[index],
            type: 'bar',
            stack: isStack ? 'total' : undefined,
            data: data[key],
            itemStyle: {
                color: index === 0 ? '#ff4d4f' : '#ffa39e'
            },
            label: {
                show: true,
                position: 'top',
                formatter: function(params) {
                    return isAmount ? formatAmount(params.value) : formatNumber(params.value);
                }
            }
        }))
    };
    
    chart.setOption(option);
    
    window.addEventListener('resize', () => {
        chart.resize();
    });
}

// 添加当前渠道和年份变量
let currentChannel = '抖音';
let currentYear = '';
let currentFile = '';

// 获取数据并更新页面
async function fetchAndUpdateData() {
    try {
        const response = await fetch(`/api/data/${currentYear}/${currentFile}?channel=${currentChannel}`);
        const data = await response.json();
        
        // 更新标题
        let titleText = currentChannel === '合计' ? 
            '全渠道数据分析' : 
            `${currentChannel}数据分析`;
        document.querySelector('h1').textContent = titleText;
        
        // 更新数据卡片
        document.getElementById('totalRecords').textContent = formatNumber(data.total_records);
        document.getElementById('totalVisits').textContent = formatNumber(data.total_first_second_visits);
        document.getElementById('totalAmount').textContent = formatAmount(data.total_first_second_amount);
        
        // 更新消费数据和投产比
        document.getElementById('channelCost').textContent = formatAmount(data.cost_data.cost);
        const roiElement = document.getElementById('roi');
        roiElement.textContent = formatROI(data.cost_data.roi);
        
        // 移除旧的颜色类
        roiElement.classList.remove('roi-good', 'roi-normal', 'roi-bad');
        // 添加新的颜色类
        roiElement.classList.add(getROIColorClass(data.cost_data.roi));
        
        // 初始化图表
        initChart('amountChart', '新客成交金额占比', data.amount_data);
        initChart('visitChart', '新客到院人数占比', data.visit_data);
        initChart('dealChart', '新客成交人数占比', data.deal_data);
        
        // 初始化柱状图
        initBarChart('consultantChart', '网电咨询师到院人数统计', 
            data.consultant_data, ['首次到院', '二次到院'], 
            ['first_visits', 'second_visits'], '人数',
            false,  // isAmount
            true   // isStack - 启用堆叠效果
        );
            
        initBarChart('consultantDealChart', '网电咨询师成交人数统计', 
            data.consultant_deal_data, ['首次成交', '二次成交'], 
            ['first_deals', 'second_deals'], '人数',
            false,  // isAmount
            true   // isStack
        );
            
        initBarChart('consultantAmountChart', '网电咨询师成交金额统计', 
            data.consultant_amount_data, ['首次成交金额', '二次成交金额'], 
            ['first_amounts', 'second_amounts'], '金额',
            true,   // isAmount
            true    // isStack
        );
            
        // 初始化折线图
        initLineChart('visitRateChart', '网电咨询师到院率统计', 
            data.visit_rate_data, ['首次到院率', '二次到院率'], 
            ['first_visit_rates', 'second_visit_rates'], '到院率(%)'
        );

        initLineChart('dealRateChart', '网电咨询师成交率统计', 
            data.deal_rate_data, ['首次成交率', '二次成交率'], 
            ['first_deal_rates', 'second_deal_rates'], '成交率(%)'
        );

        initLineChart('amountPerDealChart', '网电咨询师成交单体统计', 
            data.amount_per_deal_data, ['首次成交单体', '二次成交单体'], 
            ['first_amount_per_deals', 'second_amount_per_deals'], '成交单体',
            true  // isAmount
        );
            
        initBarChart('consultantRecordsChart', '网电咨询师建档量统计', 
            {
                names: data.consultant_records_data.names,
                records: data.consultant_records_data.records
            },
            ['建档量'],  // legendNames 
            ['records'], // dataKeys
            '建档量',    // yAxisName
            false,      // isAmount
            false       // isStack
        );

    } catch (error) {
        console.error('Error:', error);
    }
}

// 初始化年份切换功能
function initYearTabs() {
    const yearTabs = document.querySelectorAll('.year-btn');
    yearTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            yearTabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            currentYear = tab.dataset.year;

            // 显示对应年份的月份组
            const monthGroups = document.querySelectorAll('.month-group');
            monthGroups.forEach(group => {
                group.classList.remove('active');
                if (group.dataset.year === currentYear) {
                    group.classList.add('active');
                    // 选择该年份的第一个月份
                    const firstMonth = group.querySelector('.tab-btn');
                    if (firstMonth) {
                        // 清除所有月份的active状态
                        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
                        firstMonth.classList.add('active');
                        currentFile = firstMonth.dataset.file;
                        fetchAndUpdateData();
                    }
                }
            });
        });
    });
}

// 初始化文件切换功能
function initFileTabs() {
    const tabs = document.querySelectorAll('.tab-btn');
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // 只清除同一年份组内的active状态
            const parentGroup = tab.closest('.month-group');
            if (parentGroup) {
                parentGroup.querySelectorAll('.tab-btn').forEach(t => t.classList.remove('active'));
            }
            tab.classList.add('active');
            currentFile = tab.dataset.file;
            currentYear = tab.dataset.year;
            fetchAndUpdateData();
        });
    });
}

// 初始化渠道切换功能
function initChannelTabs() {
    const tabs = document.querySelectorAll('.channel-btn');
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            tabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            currentChannel = tab.dataset.channel;
            fetchAndUpdateData();
        });
    });
}

// 初始化默认选择 - 选择最近的年份和月份
function initDefaults() {
    // 获取所有年份并排序，选择最新的年份
    const years = Object.keys(window.yearsData).sort((a, b) => parseInt(b) - parseInt(a));
    const latestYear = years[0];

    if (latestYear) {
        currentYear = latestYear;

        // 定义月份顺序映射
        const monthOrder = {
            '1月': 1, '2月': 2, '3月': 3, '4月': 4, '5月': 5, '6月': 6,
            '7月': 7, '8月': 8, '9月': 9, '10月': 10, '11月': 11, '12月': 12,
            '一月': 1, '二月': 2, '三月': 3, '四月': 4, '五月': 5, '六月': 6,
            '七月': 7, '八月': 8, '九月': 9, '十月': 10, '十一月': 11, '十二月': 12
        };

        // 获取该年份的所有文件并按月份排序，选择最新的月份
        const files = window.yearsData[latestYear];
        const sortedFiles = files.sort((a, b) => {
            const monthA = a.replace('.xlsx', '');
            const monthB = b.replace('.xlsx', '');
            const orderA = monthOrder[monthA] || 0;
            const orderB = monthOrder[monthB] || 0;
            return orderB - orderA; // 降序排列，最新月份在前
        });

        if (sortedFiles.length > 0) {
            currentFile = sortedFiles[0]; // 选择最新的月份
        }
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    initDefaults();
    initYearTabs();
    initFileTabs();
    initChannelTabs();
    fetchAndUpdateData();
});

// 在 initBarChart 函数后添加
function initLineChart(elementId, title, data, legendNames, dataKeys, yAxisName, isAmount = false) {
    const container = document.getElementById(elementId);
    if (!container) {
        console.error(`找不到元素: ${elementId}`);
        return;
    }

    const chart = echarts.init(container);
    const option = {
        title: {
            text: title,
            left: 'center'
        },
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                let result = `${params[0].name}<br/>`;
                params.forEach(param => {
                    const value = isAmount ? 
                        formatAmount(param.value) : 
                        param.value.toFixed(2) + (title.includes('率') ? '%' : '');
                    result += `${param.seriesName}：${value}<br/>`;
                });
                return result;
            }
        },
        legend: {
            data: legendNames,
            top: 30
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: data.names,
            axisLabel: {
                interval: 0,
                rotate: 30
            }
        },
        yAxis: {
            type: 'value',
            name: yAxisName,
            axisLabel: {
                formatter: function(value) {
                    return isAmount ? formatAmount(value) : value + (title.includes('率') ? '%' : '');
                }
            }
        },
        series: [
            {
                name: legendNames[0],
                type: 'line',
                data: data[dataKeys[0]],
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                label: {
                    show: true,
                    formatter: function(params) {
                        return isAmount ? 
                            formatAmount(params.value) : 
                            params.value.toFixed(2) + (title.includes('率') ? '%' : '');
                    }
                }
            },
            {
                name: legendNames[1],
                type: 'line',
                data: data[dataKeys[1]],
                smooth: true,
                symbol: 'circle',
                symbolSize: 8,
                label: {
                    show: true,
                    formatter: function(params) {
                        return isAmount ? 
                            formatAmount(params.value) : 
                            params.value.toFixed(2) + (title.includes('率') ? '%' : '');
                    }
                }
            }
        ]
    };
    
    chart.setOption(option);
    
    window.addEventListener('resize', () => {
        chart.resize();
    });
} 