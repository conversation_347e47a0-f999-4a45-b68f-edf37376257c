import requests
import json

def get_tencent_ad_data():
    """
    从腾讯广告API获取数据并提取account_id、adgroup_id和cost信息
    
    返回:
        list: 包含提取数据的字典列表
    """
    headers = {
        "accept": "application/json, text/javascript, */*; q=0.01",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "content-type": "application/json",
        "enable-downgrade": "true",
        "g-tk": "*********",
        "origin": "https://ad.qq.com",
        "priority": "u=1, i",
        "referer": "https://ad.qq.com/atlas/********/admanage/index?tab=adgroup&query={\"operation_status\":[\"CALCULATE_STATUS_EXCLUDE_DEL\"],\"system_status\":[]}",
        "sec-ch-ua": "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "x-requested-with": "XMLHttpRequest",
        "x-trace-id": "f0c28865-d2ea-ac2a-3cf2-b6ee5b1b5f72",
        "x-trans-id": "d4f1ac82-baa3-5543-ac1f-8eb993f578b6",
        "x-tsa-client-authentication": "true"
    }
    
    cookies = {
        "pgv_pvi": "215352320",
        "pgv_pvid": "7184156880",
        "RK": "zzkVm3+K9Q",
        "ptcz": "2ebd43fddeedfc40d0e480ccb800aafcb03db337d800c998721e120d89914107",
        "_clck": "ggqmoc|1|fsp|0",
        "qq_domain_video_guid_verify": "0420327f036a3efa",
        "_qimei_uuid42": "19219102c16100571ebc3bce06f6cc6a59cae187eb",
        "_qimei_fingerprint": "15130563152950178be3c6e90e226d30",
        "_qimei_q36": "",
        "_qimei_h38": "8f95aa661ebc3bce06f6cc6a02000002b19219",
        "gdt_token": "TGT-72486-mCm2zttFUZcgcj7pZzWuuCcgEF3uthxMS9woKNQoqOB4d8CaYI2ClhC.H7M_bJSH",
        "gdt_protect": "72ccea75feffa7b75b302d6f3e33b8a88db510fd",
        "atlas_platform": "atlas",
        "questionnaire_dialog": "1",
        "BM_SSO_UID_LIST": "\"********,********,********,********,********\""
    }
    
    url = "https://ad.qq.com/api/v3.0/integrated_list_multiaccount/get"
    
    params = {
        "nonce": "**********",
        "timestamp": "**********"
    }
    
    data = {
        "account_id_list": [********],
        "page": 1,
        "page_size": 20,
        "date_range": {
            "start_date": "2025-03-28",
            "end_date": "2025-04-02"
        },
        "time_line": "REQUEST_TIME",
        "is_total": False,
        "fields": [
            "account_id", "adgroup.adgroup_id", "adgroup.adgroup_name", "adgroup.is_deleted",
            "adgroup.smart_delivery_platform", "adgroup.smart_delivery_scene", "adgroup.marketing_target_type",
            "adgroup.marketing_goal", "adgroup.marketing_sub_goal", "adgroup.marketing_carrier_type",
            "adgroup.marketing_carrier_detail", "adgroup.optimization_goal", "adgroup.marketing_target_type_cn",
            "adgroup.marketing_goal_cn", "adgroup.marketing_carrier_type_cn", "adgroup.optimization_goal_cn",
            "adgroup.deep_coversion_worth_goal_cn", "adgroup.deep_coversion_behavior_goal_cn",
            "adgroup.deep_coversion_behavior_advanced_goal_cn", "adgroup.deep_coversion_worth_advanced_goal_cn",
            "adgroup.og_completion_type", "adgroup.system_status", "adgroup.system_status_cn",
            "adgroup.created_by_industry_platform", "adgroup.auto_acquisition_status_text",
            "adgroup.auto_acquisition_status_message", "adgroup.auto_acquisition_status",
            "adgroup.auto_acquisition_enabled", "adgroup.auto_acquisition_budget",
            "adgroup.cost_guarantee_status_message", "adgroup.cost_guarantee_status", "adgroup.site_set",
            "adgroup.cloud_union_spec", "adgroup.system_status_tips", "adgroup.configured_status",
            "adgroup.material_package_id", "adgroup.mpa_spec", "adgroup.dca_spec", "adgroup.flow_lock_status",
            "adgroup.bid_amount", "adgroup.smart_bid_type", "adgroup.smart_bid_type_cn", "adgroup.bid_mode",
            "adgroup.bid_scene", "adgroup.site_set_cn", "adgroup.priority_site_set", "adgroup.exploration_strategy",
            "adgroup.automatic_site_enabled", "adgroup.targeting_translation", "adgroup.daily_budget",
            "adgroup.begin_date", "adgroup.end_date", "adgroup.time_series", "adgroup.first_day_begin_time",
            "adgroup.deep_conversion_spec", "adgroup.deep_conversion_behavior_bid",
            "adgroup.deep_conversion_behavior_advanced_bid", "adgroup.deep_conversion_worth_rate",
            "adgroup.deep_conversion_worth_advanced_rate", "adgroup.is_rta", "adgroup.ad_approval_status",
            "adgroup.rta_policy_uuid", "adgroup.incubation_optimization_goal",
            "adgroup.incubation_optimization_goal_cn", "adgroup.ecom_pkam_switch", "report.view_count",
            "report.valid_click_count", "report.ctr", "report.cpc", "report.cost", "report.conversions_count",
            "report.conversions_cost"
        ],
        "level": "ADGROUP",
        "group_by": ["adgroup_id"],
        "filtering": [
            {
                "field": "adgroup.operation_status",
                "operator": "EQUALS",
                "values": ["CALCULATE_STATUS_EXCLUDE_DEL"]
            },
            {
                "field": "adgroup.brand_ad_type",
                "operator": "EQUALS",
                "values": ["BRAND_AD_TYPE_NONE"]
            },
            {
                "field": "adgroup.smart_delivery_platform",
                "operator": "LESS",
                "values": ["SMART_DELIVERY_PLATFORM_EDITION_SCENE"]
            }
        ]
    }
    
    try:
        response = requests.post(url, headers=headers, cookies=cookies, params=params, json=data)
        response_data = response.json()
        
        # 提取所需数据
        result = []
        if response_data.get('code') == 0 and 'data' in response_data and 'list' in response_data['data']:
            for item in response_data['data']['list']:
                extracted_data = {
                    'account_id': item.get('account_id'),
                    'adgroup_id': item.get('adgroup', {}).get('adgroup_id'),
                    'cost': item.get('report', {}).get('cost')
                }
                result.append(extracted_data)
        
        return result
    except Exception as e:
        print(f"获取数据时出错: {e}")
        return []

# 测试函数
if __name__ == "__main__":
    ad_data = get_tencent_ad_data()
    print(json.dumps(ad_data, indent=2, ensure_ascii=False))
