import os
from flask import Flask, render_template, jsonify, request
import pandas as pd
# import time
# import threading
# from classify_model.process_data import classify_data  # 假设 classify_data 是处理分类的函数
from get_promotion import get_promotion_data
app = Flask(__name__)

def get_excel_files():
    # 获取lib目录下按年份组织的Excel文件
    lib_path = 'lib'
    years_data = {}

    # 遍历年份目录
    for year_dir in os.listdir(lib_path):
        year_path = os.path.join(lib_path, year_dir)
        if os.path.isdir(year_path):
            # 获取该年份目录下的Excel文件
            excel_files = [
                f for f in os.listdir(year_path)
                if f.endswith('.xlsx') and not f.startswith('~$')
            ]

            # 定义月份顺序
            month_order = {
                '一月': 1, '二月': 2, '三月': 3, '四月': 4, '五月': 5, '六月': 6,
                '七月': 7, '八月': 8, '九月': 9, '十月': 10, '十一月': 11, '十二月': 12,
                '1月': 1, '2月': 2, '3月': 3, '4月': 4, '5月': 5, '6月': 6,
                '7月': 7, '8月': 8, '9月': 9, '10月': 10, '11月': 11, '12月': 12
            }

            # 排序文件
            def get_month_number(filename):
                # 移除文件扩展名和其他字符，只保留月份部分
                month = filename.replace('.xlsx', '')
                # 返回月份对应的数字，如果找不到对应关系则返回13（确保未知月份排在最后）
                return month_order.get(month, 13)

            # 按月份顺序排序
            excel_files.sort(key=get_month_number)
            years_data[year_dir] = excel_files

    return years_data

def get_channel_data(filename='10月.xlsx', channel='抖音', year='2024'):
    # 读取数据文件，设置header参数来处理多级表头
    file_path = os.path.join(os.getcwd(), 'lib', year, filename)
    print('文件路径-----------', file_path)
    df = pd.read_excel(file_path, header=[2,3])
    
    # 处理前三列的合并单元格（向前填充空值）
    df.iloc[:, 0] = df.iloc[:, 0].fillna(method='ffill')
    df.iloc[:, 1] = df.iloc[:, 1].fillna(method='ffill')
    df.iloc[:, 2] = df.iloc[:, 2].fillna(method='ffill')
    
    if channel == '合计':
        # 合并所有渠道的数据
        channels = ['抖音', '朋友圈', '百度', '快手', '高德', '美团']
        all_channel_df = pd.DataFrame()
        all_channel_total_df = pd.DataFrame()
        
        for ch in channels:
            # 获取每个渠道的条件
            if ch == '百度':
                channel_condition = df.iloc[:, 0].str.contains('竞价', na=False)
            elif ch in ['高德', '美团']:
                channel_condition = df.iloc[:, 0].str.contains('电商', na=False)
            else:  # 抖音、朋友圈、快手
                channel_condition = df.iloc[:, 0].str.contains('信息流', na=False)
            
            # 获取该渠道的数据
            temp_df = df[
                channel_condition &
                (df.iloc[:, 1].str.contains(ch, na=False)) &
                (~df.iloc[:, 2].str.contains('小计|汇总', na=False))
            ]
            
            # 获取该渠道的小计数据
            temp_total_df = df[
                channel_condition &
                (df.iloc[:, 1].str.contains(ch, na=False)) &
                (df.iloc[:, 2].str.contains('小计', na=False))
            ]
            
            all_channel_df = pd.concat([all_channel_df, temp_df])
            all_channel_total_df = pd.concat([all_channel_total_df, temp_total_df])
        
        # 使用合并后的数据
        channel_df = all_channel_df
        channel_total_df = all_channel_total_df
    else:
        # 根据不同渠道设置不同的筛选条件
        if channel == '百度':
            channel_condition = df.iloc[:, 0].str.contains('竞价', na=False)
        elif channel in ['高德', '美团']:
            channel_condition = df.iloc[:, 0].str.contains('电商', na=False)
        else:  # 抖音、朋友圈、快手
            channel_condition = df.iloc[:, 0].str.contains('信息流', na=False)
        
        # 获取指定渠道的小计行数据
        channel_total_df = df[
            channel_condition &  # 使用对应的渠道条件
            (df.iloc[:, 1].str.contains(channel, na=False)) &   # 三级渠道是指定渠道
            (df.iloc[:, 2].str.contains('小计', na=False))      # 包含小计的行
        ]
        
        # 筛选数据（不包含小计和汇总的行）
        channel_df = df[
            channel_condition &  # 使用对应的渠道条件
            (df.iloc[:, 1].str.contains(channel, na=False)) &   # 三级渠道是指定渠道
            (~df.iloc[:, 2].str.contains('小计|汇总', na=False))  # 排除小计和汇总
        ]
    
    # 打印调试信息
    print(f"\n{channel}小计数据：")
    print(channel_total_df)
    
    # 计算合计数据
    channel_total_records = 0
    channel_total_visits = 0
    channel_total_amount = 0
    if not channel_total_df.empty:
        channel_total_records = channel_total_df[('建档量', 'Unnamed: 3_level_1')].sum()
        channel_total_visits = channel_total_df[('来院人数', '新客首次')].sum()
        channel_total_amount = (
            channel_total_df[('成交金额', '新客首次')].sum() +
            channel_total_df[('成交金额', '新客二次')].sum()
        )
    
    print("\n抖音数据预览：")
    print(channel_df.iloc[:, :3])  # 打印前三列数据预览
    print(f"抖音数据行数：{len(channel_df)}")
    print(f"抖音建档总数：{channel_total_records}")
    print(f"抖音到院总人数：{channel_total_visits}")
    print(f"抖音成交总金额：{channel_total_amount}")
    
    # 获取网电咨询师的到院数据、成交数据和成交金额数据
    consultant_data = {}  # 改为字典，用于合并相同咨询师的数据
    consultant_deal_data = {}  # 改为字典
    consultant_amount_data = {}  # 改为字典
    consultant_records = {}  # 用于存储每个咨询师的建档量总和

    for _, row in channel_df.iterrows():
        # 获取咨询师名称
        consultant_name = row[('网电咨询师', 'Unnamed: 2_level_1')]
        # 获取建档量
        records = float(row[('建档量', 'Unnamed: 3_level_1')])
        
        # 累加建档量到字典中
        if consultant_name in consultant_records:
            consultant_records[consultant_name] += records
        else:
            consultant_records[consultant_name] = records
        
        # 到院数据
        if consultant_name not in consultant_data:
            consultant_data[consultant_name] = {
                'name': consultant_name,
                'first_visit': 0,
                'second_visit': 0
            }
        consultant_data[consultant_name]['first_visit'] += row[('来院人数', '新客首次')]
        consultant_data[consultant_name]['second_visit'] += row[('来院人数', '新客二次')]
        
        # 成交人数数据
        if consultant_name not in consultant_deal_data:
            consultant_deal_data[consultant_name] = {
                'name': consultant_name,
                'first_deal': 0,
                'second_deal': 0
            }
        consultant_deal_data[consultant_name]['first_deal'] += row[('成交人数', '新客首次')]
        consultant_deal_data[consultant_name]['second_deal'] += row[('成交人数', '新客二次')]
        
        # 成交金额数据
        if consultant_name not in consultant_amount_data:
            consultant_amount_data[consultant_name] = {
                'name': consultant_name,
                'first_amount': 0,
                'second_amount': 0
            }
        consultant_amount_data[consultant_name]['first_amount'] += row[('成交金额', '新客首次')]
        consultant_amount_data[consultant_name]['second_amount'] += row[('成交金额', '新客二次')]

    # 将字典转换为列表
    consultant_data = list(consultant_data.values())
    consultant_deal_data = list(consultant_deal_data.values())
    consultant_amount_data = list(consultant_amount_data.values())
    
    # 将建档量字典转换为排序后的列表
    consultant_records_data = []
    for name, records in consultant_records.items():
        consultant_records_data.append({
            'name': name,
            'records': records
        })
    
    # 按建档量降序排序
    consultant_records_data.sort(key=lambda x: x['records'], reverse=True)
    
    # 取前10名咨询师
    consultant_records_data = consultant_records_data[:10]
    
    # 准备返回的建档量数据格式
    consultant_records_chart_data = {
        'names': [item['name'] for item in consultant_records_data],
        'records': [item['records'] for item in consultant_records_data]
    }
    
    # 按到院总人数（首次+二次）降序排序
    consultant_data.sort(key=lambda x: x['first_visit'] + x['second_visit'], reverse=True)
    # 按成交总人数（首次+二次）降序排序
    consultant_deal_data.sort(key=lambda x: x['first_deal'] + x['second_deal'], reverse=True)
    # 按成交总金额（首次+二次）降序排序
    consultant_amount_data.sort(key=lambda x: x['first_amount'] + x['second_amount'], reverse=True)
    
    # 取前10名咨询师
    consultant_data = consultant_data[:12]
    consultant_deal_data = consultant_deal_data[:12]
    consultant_amount_data = consultant_amount_data[:12]
    
    # 读取消费数据
    try:
        cost_df = pd.read_excel('./data/每月消費.xlsx')
        # 获取当前月份（从文件名中提取）
        current_month = filename.replace('.xlsx', '')
        current_year = int(year)

        # 如果是合计模式，计算所有渠道的消费总和
        if channel == '合计':
            channels = ['抖音', '朋友圈', '百度', '快手', '高德', '美团']
            channel_cost = 0
            for ch in channels:
                cost_data = cost_df[
                    (cost_df['年份'] == current_year) &
                    (cost_df['月份'] == current_month) &
                    (cost_df['渠道'] == ch)
                ]
                if not cost_data.empty:
                    channel_cost += float(cost_data['消费'].iloc[0])
        else:
            # 获取对应年份、月份和渠道的消费数据
            cost_data = cost_df[
                (cost_df['年份'] == current_year) &
                (cost_df['月份'] == current_month) &
                (cost_df['渠道'] == channel)
            ]
            channel_cost = float(cost_data['消费'].iloc[0]) if not cost_data.empty else 0
        
        # 计算投产比 (ROI)
        roi = round(channel_total_amount / channel_cost, 2) if channel_cost > 0 else 0
    except Exception as e:
        print(f"读取消费数据失败: {e}")
        channel_cost = 0
        roi = 0

    # 计算到院率数据
    visit_rate_data = []
    for item in consultant_data:  # 使用 consultant_data 中的数据
        name = item['name']
        records = channel_df[
            channel_df[('网电咨询师', 'Unnamed: 2_level_1')] == name
        ][('建档量', 'Unnamed: 3_level_1')].values[0]
        
        if records > 0:  # 避免除以零
            first_visit_rate = round(item['first_visit'] / records * 100, 2)
            second_visit_rate = round(item['second_visit'] / records * 100, 2)
            
            # 排除到院率超过100%的数据
            if first_visit_rate <= 100 and second_visit_rate <= 100:
                visit_rate_data.append({
                    'name': name,
                    'first_visit_rate': first_visit_rate,
                    'second_visit_rate': second_visit_rate
                })
    
    # 计算成交率数据
    deal_rate_data = []
    for item in consultant_deal_data:
        name = item['name']
        # 获取该咨询师的到院人数
        consultant_visit = next((x for x in consultant_data if x['name'] == name), None)
        
        if consultant_visit and (consultant_visit['first_visit'] > 0 or consultant_visit['second_visit'] > 0):
            first_deal_rate = round(item['first_deal'] / consultant_visit['first_visit'] * 100, 2) if consultant_visit['first_visit'] > 0 else 0
            second_deal_rate = round(item['second_deal'] / consultant_visit['second_visit'] * 100, 2) if consultant_visit['second_visit'] > 0 else 0
            
            # 排除成交率超过100%的数据
            if first_deal_rate <= 100 and second_deal_rate <= 100:
                deal_rate_data.append({
                    'name': name,
                    'first_deal_rate': first_deal_rate,
                    'second_deal_rate': second_deal_rate
                })
    
    # 计算成交单体数据
    amount_per_deal_data = []
    for item in consultant_deal_data:  # 使用 consultant_deal_data 中的数据
        name = item['name']
        first_deals = item['first_deal']
        second_deals = item['second_deal']
        
        consultant_amounts = next(
            (x for x in consultant_amount_data if x['name'] == name),
            {'first_amount': 0, 'second_amount': 0}
        )
        
        first_amount_per_deal = round(consultant_amounts['first_amount'] / first_deals, 2) if first_deals > 0 else 0
        second_amount_per_deal = round(consultant_amounts['second_amount'] / second_deals, 2) if second_deals > 0 else 0
        
        amount_per_deal_data.append({
            'name': name,
            'first_amount_per_deal': first_amount_per_deal,
            'second_amount_per_deal': second_amount_per_deal
        })
    
    # 按到院率总和（首次+二次）降序排序
    visit_rate_data.sort(key=lambda x: x['first_visit_rate'] + x['second_visit_rate'], reverse=True)
    # 按成交率总和（首次+二次）降序排序
    deal_rate_data.sort(key=lambda x: x['first_deal_rate'] + x['second_deal_rate'], reverse=True)
    # 按成交单体总和（首次+二次）降序排序
    amount_per_deal_data.sort(key=lambda x: x['first_amount_per_deal'] + x['second_amount_per_deal'], reverse=True)
    
    # 打印调试信息
    print("\n建档量数据预览：")
    print(channel_df[[('网电咨询师', 'Unnamed: 2_level_1'), ('建档量', 'Unnamed: 3_level_1')]].head())
    print(f"建档量数据条数：{len(consultant_records_data)}")
    for item in consultant_records_data[:3]:  # 打印前三条数据
        print(f"咨询师：{item['name']}, 建档量：{item['records']}")
    
    # 计算所需数据
    data = {
        'total_records': channel_total_records,
        'total_first_second_visits': channel_total_visits,
        'total_first_second_amount': channel_total_amount,
        
        # 饼图1数据：成交金额占比
        'amount_data': [
            {
                'name': '新客首次成交金额',
                'value': float(channel_df[('成交金额', '新客首次')].sum())
            },
            {
                'name': '新客二次成交金额',
                'value': float(channel_df[('成交金额', '新客二次')].sum())
            }
        ],
        
        # 饼图2数据：到院人数占比
        'visit_data': [
            {
                'name': '新客首次到院人数',
                'value': int(channel_df[('来院人数', '新客首次')].sum())
            },
            {
                'name': '新客二次到院人数',
                'value': int(channel_df[('来院人数', '新客二次')].sum())
            }
        ],
        
        # 饼图3数据：成交人数占比
        'deal_data': [
            {
                'name': '新客首次成交人数',
                'value': int(channel_df[('成交人数', '新客首次')].sum())
            },
            {
                'name': '新客二次成交人数',
                'value': int(channel_df[('成交人数', '新客二次')].sum())
            }
        ],
        
        # 添加咨询师数据
        'consultant_data': {
            'names': [item['name'] for item in consultant_data],
            'first_visits': [item['first_visit'] for item in consultant_data],
            'second_visits': [item['second_visit'] for item in consultant_data]
        },
        
        # 添加咨询师成交数据
        'consultant_deal_data': {
            'names': [item['name'] for item in consultant_deal_data],
            'first_deals': [item['first_deal'] for item in consultant_deal_data],
            'second_deals': [item['second_deal'] for item in consultant_deal_data]
        },
        
        # 添加咨询师成交金额数据
        'consultant_amount_data': {
            'names': [item['name'] for item in consultant_amount_data],
            'first_amounts': [item['first_amount'] for item in consultant_amount_data],
            'second_amounts': [item['second_amount'] for item in consultant_amount_data]
        },
        
        'cost_data': {
            'cost': channel_cost,
            'roi': roi
        },
        
        'visit_rate_data': {
            'names': [item['name'] for item in visit_rate_data],
            'first_visit_rates': [item['first_visit_rate'] for item in visit_rate_data],
            'second_visit_rates': [item['second_visit_rate'] for item in visit_rate_data]
        },
        'deal_rate_data': {
            'names': [item['name'] for item in deal_rate_data],
            'first_deal_rates': [item['first_deal_rate'] for item in deal_rate_data],
            'second_deal_rates': [item['second_deal_rate'] for item in deal_rate_data]
        },
        'amount_per_deal_data': {
            'names': [item['name'] for item in amount_per_deal_data],
            'first_amount_per_deals': [item['first_amount_per_deal'] for item in amount_per_deal_data],
            'second_amount_per_deals': [item['second_amount_per_deal'] for item in amount_per_deal_data]
        },
        # 添加建档量数据
        'consultant_records_data': consultant_records_chart_data
    }
    
    # 打印数据以便调试
    print("\n抖音数据统计:")
    print(f"总记录数: {data['total_records']}")
    print(f"到院总人数: {data['total_first_second_visits']}")
    print(f"成交总金额: {data['total_first_second_amount']}")
    print(f"饼图数据1: {data['amount_data']}")
    
    return data

def get_date_range(df, date_column):
    """
    获取日期范围，默认显示最近7天数据
    如果数据不足7天，则显示全部数据
    """
    max_date = df[date_column].max().strftime('%Y-%m-%d')
    min_date = df[date_column].min().strftime('%Y-%m-%d')
    
    # 计算默认的开始日期（最大日期前6天）
    default_start = (df[date_column].max() - pd.Timedelta(days=6)).strftime('%Y-%m-%d')
    # 如果默认开始日期早于数据最早日期，则使用最早日期
    default_start = max(default_start, min_date)
    
    return min_date, max_date, default_start

@app.route('/')
def index():
    years_data = get_excel_files()

    # 对年份进行排序（最新年份在前）
    sorted_years_data = {}
    sorted_years = sorted(years_data.keys(), key=int, reverse=True)

    # 定义月份顺序
    month_order = {
        '1月': 1, '2月': 2, '3月': 3, '4月': 4, '5月': 5, '6月': 6,
        '7月': 7, '8月': 8, '9月': 9, '10月': 10, '11月': 11, '12月': 12,
        '一月': 1, '二月': 2, '三月': 3, '四月': 4, '五月': 5, '六月': 6,
        '七月': 7, '八月': 8, '九月': 9, '十月': 10, '十一月': 11, '十二月': 12
    }

    def get_month_number(filename):
        month = filename.replace('.xlsx', '')
        return month_order.get(month, 0)

    # 找到最新年份和最新月份
    latest_year = sorted_years[0] if sorted_years else None
    latest_file = None

    for year in sorted_years:
        # 按月份顺序排序，最新月份在前
        sorted_files = sorted(years_data[year], key=get_month_number, reverse=True)
        sorted_years_data[year] = sorted_files

        # 记录最新年份的最新月份
        if year == latest_year and sorted_files:
            latest_file = sorted_files[0]

    return render_template('index.html',
                         years_data=sorted_years_data,
                         latest_year=latest_year,
                         latest_file=latest_file)

@app.route('/api/data/<year>/<filename>')
def get_data(year, filename):
    channel = request.args.get('channel', '抖音')  # 从查询参数获取渠道
    try:
        data = get_channel_data(filename, channel, year)
        return jsonify(data)
    except Exception as e:
        print("错误:", str(e))
        return jsonify({'error': str(e)}), 500

@app.route('/feiyu')
def feiyu():
    # 获取查询参数中的开始和结束时间
    end_date = request.args.get('end_date')
    start_date = request.args.get('start_date')
 
    
    # 读取有效统计数据
    leads_df = pd.read_excel(r'F:\文件\工作文件\每月数据\最近一周有效统计.xlsx', dtype=str)
    
    # 先筛选日期范围
    leads_df['线索创建时间'] = pd.to_datetime(leads_df['线索创建时间'])
    
    # 获取日期范围
    min_date, max_date, default_start = get_date_range(leads_df, '线索创建时间')
    
    # 如果没有指定日期，使用默认值
    if not end_date:
        end_date = max_date
    if not start_date:
        start_date = default_start
    # 读取飞鱼消费数据
    cost_df = get_promotion_data(start_date, end_date)
    
    # 筛选日期范围内的数据
    leads_df = leads_df[(leads_df['线索创建时间'].dt.strftime('%Y-%m-%d') >= start_date) & 
                        (leads_df['线索创建时间'].dt.strftime('%Y-%m-%d') <= end_date)]
    
    # 确保数值列的类型正确
    print(leads_df['开单业绩汇总'].unique())
    # 先清理开单业绩汇总中的逗号
    leads_df['开单业绩汇总'] = leads_df['开单业绩汇总'].astype(str).str.replace(',', '')
    leads_df['开单业绩汇总'] = pd.to_numeric(leads_df['开单业绩汇总'], errors='coerce').fillna(0)
    cost_df['消耗'] = pd.to_numeric(cost_df['消耗'], errors='coerce').fillna(0)
    
    # 清理广告ID中的制表符和空白字符
    leads_df['广告id'] = leads_df['广告id'].str.strip()
    cost_df['广告id'] = cost_df['广告id'].str.strip()
    
    # 先按广告ID分组计算消耗
    cost_by_ad = cost_df.groupby(by=['广告id','账户'])['消耗'].sum().reset_index()
    cost_by_ad.to_excel('cost_by_ad.xlsx')
    # 打印调试信息
    print("\n开单业绩汇总的唯一值:")
    print(leads_df['开单业绩汇总'].unique())
    print("\n备注的唯一值:")
    print(leads_df['备注'].unique())
    
    # 按广告ID分组计算leads数据
    leads_by_ad = leads_df.groupby('广告id').agg({
        '备注': lambda x: (x == '有效').sum(),  # 有效数
        '是否到院': lambda x, y=leads_df['备注']: ((x == '是') & (y != '重单')).sum(),  # 到院数
        '开单业绩汇总': lambda x, y=leads_df['备注']: x[y != '重单'].astype(float).sum()   # 确保转换为float后再求和
    }).reset_index()
    
    # 合并两个数据表
    leads_by_ad.to_excel('leads_by_ad.xlsx')
    ad_stats = pd.merge(leads_by_ad, cost_by_ad, on='广告id', how='outer')
    ad_account_stats = ad_stats
    ad_account_stats.to_excel('ad_account_stats.xlsx')
    # 计算转化成本、到院成本和投产比
    ad_stats['转化成本'] = ad_stats['消耗'] / ad_stats['备注'].where(ad_stats['备注'] > 0, float('inf'))
    ad_stats['到院成本'] = ad_stats['消耗'] / ad_stats['是否到院'].where(ad_stats['是否到院'] > 0, float('inf'))
    ad_stats['投产比'] = ad_stats['开单业绩汇总'] / ad_stats['消耗'].where(ad_stats['消耗'] > 0, float('inf'))
    
    # 将无限值替换为0
    ad_stats = ad_stats.replace([float('inf')], 0)
    
    # 按总消耗降序排序
    ad_stats = ad_stats.sort_values('消耗', ascending=False)
    
    # 将数据转换为列表格式
    ad_stats_list = []
    for _, row in ad_stats.iterrows():
        ad_stats_list.append({
            'ad_id': row['广告id'],
            'account': row['账户'],  # 添加账户信息
            'total_cost': round(row['消耗'], 2),
            'conversion_count': int(row['备注']) if pd.notna(row['备注']) else 0,
            'conversion_cost': round(row['转化成本'], 2),
            'visit_count': int(row['是否到院']) if pd.notna(row['是否到院']) else 0,
            'visit_cost': round(row['到院成本'], 2),
            'total_amount': round(row['开单业绩汇总'], 2),
            'roi': round(row['投产比'], 2)
        })
    
    # 计算基础统计数据
    total_phones = len(leads_df)
    valid_leads = len(leads_df[~leads_df['补充信息'].str.contains('隐私', na=False)])
    
    # 统计线索标签分布
    label_data = leads_df['线索标签'].value_counts().to_dict()
    label_series = [{'name': k, 'value': v} for k, v in label_data.items()]
    
    # 统计线索阶段分布
    stage_data = leads_df['线索阶段'].value_counts().to_dict()
    stage_series = [{'name': k, 'value': v} for k, v in stage_data.items()]
    
    # 统计备注分布
    note_data = leads_df['备注'].value_counts().to_dict()
    note_series = [{'name': k, 'value': v} for k, v in note_data.items()]
    
    # 统计所属人的电话数量
    owner_data = leads_df['所属人'].value_counts()
    owner_names = owner_data.index.tolist()
    owner_values = owner_data.values.tolist()
    
    # 统计每个所属人的备注分布和有效率
    owner_note_stats = []
    for owner, group in leads_df.groupby('所属人'):
        total = len(group)
        note_counts = group['备注'].value_counts().to_dict()
        valid_count = group[group['备注'] == '有效'].shape[0]
        valid_rate = round(valid_count / total * 100, 2) if total > 0 else 0
        owner_note_stats.append({
            'owner': owner,
            'total': total,
            'notes': note_counts,
            'valid_rate': valid_rate
        })
    
    # 按总电话数降序排序
    owner_note_stats = sorted(owner_note_stats, key=lambda x: x['total'], reverse=True)
    
    # 统计落地页的备注分布
    landing_note_stats = []
    for url, group in leads_df.groupby('落地页链接'):
        total = len(group)
        note_counts = group['备注'].value_counts().to_dict()
        valid_count = group[group['备注'] == '有效'].shape[0]
        valid_rate = round(valid_count / total * 100, 2) if total > 0 else 0
        landing_note_stats.append({
            'url': url,
            'total': total,
            'notes': note_counts,
            'valid_rate': valid_rate
        })
    
    # 按总电话数降序排序
    landing_note_stats = sorted(landing_note_stats, key=lambda x: x['total'], reverse=True)
    
    # 统计视频ID的备注分布
    video_note_stats = []
    for video_id, group in leads_df.groupby('视频ID'):
        total = len(group)
        note_counts = group['备注'].value_counts().to_dict()
        valid_count = group[group['备注'] == '有效'].shape[0]
        valid_rate = round(valid_count / total * 100, 2) if total > 0 else 0
        video_note_stats.append({
            'video_id': str(video_id),  # 确保视频ID为字符串
            'total': total,
            'notes': note_counts,
            'valid_rate': valid_rate
        })
    
    # 按总电话数降序排序
    video_note_stats = sorted(video_note_stats, key=lambda x: x['total'], reverse=True)
    
    # 按小时统计有效率
    leads_df['hour'] = leads_df['线索创建时间'].dt.hour
    hourly_data = {
        'hours': [],
        'valid_counts': [],
        'total_counts': [],
        'valid_rates': []
    }
    
    for hour in range(24):
        hour_df = leads_df[leads_df['hour'] == hour]
        total = len(hour_df)
        valid = len(hour_df[hour_df['备注'] == '有效'])
        valid_rate = round(valid / total * 100, 2) if total > 0 else 0
        
        hourly_data['hours'].append(hour)
        hourly_data['valid_counts'].append(valid)
        hourly_data['total_counts'].append(total)
        hourly_data['valid_rates'].append(valid_rate)
    
    # 在 feiyu() 函数中，在计算 ad_stats 之后添加
    # 计算账户统计数据
    account_stats = ad_account_stats.groupby('账户').agg({
        '消耗': 'sum',
        '备注': 'sum',  # 有效数
        '是否到院': 'sum',  # 到院数
        '开单业绩汇总': 'sum'  # 成交业绩
    }).reset_index()

    # 计算每个账户的转化成本、到院成本和投产比
    account_stats['转化成本'] = account_stats['消耗'] / account_stats['备注'].where(account_stats['备注'] > 0, float('inf'))
    account_stats['到院成本'] = account_stats['消耗'] / account_stats['是否到院'].where(account_stats['是否到院'] > 0, float('inf'))
    account_stats['投产比'] = account_stats['开单业绩汇总'] / account_stats['消耗'].where(account_stats['消耗'] > 0, float('inf'))

    # 将无限值替换为0
    account_stats = account_stats.replace([float('inf')], 0)

    # 按总消耗降序排序
    account_stats = account_stats.sort_values('消耗', ascending=False)
    account_stats.to_excel('account_stats.xlsx')
    # 将数据转换为列表格式
    account_stats_list = []
    for _, row in account_stats.iterrows():
        account_stats_list.append({
            'account': row['账户'],
            'total_cost': round(row['消耗'], 2),
            'conversion_count': int(row['备注']) if pd.notna(row['备注']) else 0,
            'conversion_cost': round(row['转化成本'], 2),
            'visit_count': int(row['是否到院']) if pd.notna(row['是否到院']) else 0,
            'visit_cost': round(row['到院成本'], 2),
            'total_amount': round(row['开单业绩汇总'], 2),
            'roi': round(row['投产比'], 2)
        })

    # 获取所有账户列表
    account_list = sorted([str(account) for account in ad_account_stats['账户'].unique().tolist()])

    return render_template('feiyu.html',
                         total_phones=total_phones,
                         valid_leads=valid_leads,
                         min_date=min_date,
                         max_date=max_date,
                         start_date=start_date or min_date,
                         end_date=end_date or max_date,
                         label_series=label_series,
                         stage_series=stage_series,
                         note_series=note_series,
                         owner_names=owner_names,
                         owner_values=owner_values,
                         owner_note_stats=owner_note_stats,
                         landing_note_stats=landing_note_stats,
                         video_note_stats=video_note_stats,
                         hourly_data=hourly_data,
                         ad_stats=ad_stats_list,
                         account_stats=account_stats_list,
                         account_list=account_list)

#朋友圈数据
@app.route('/friend_circle')
def friend_circle():
    # 获取查询参数中的开始和结束时间
    end_date = request.args.get('end_date')
    start_date = request.args.get('start_date')
    
    # 读取数据文件
    df = pd.read_csv('friendcircle/2025-01-06 08 53 02.csv', encoding='gbk')
    df['线索提交时间'] = pd.to_datetime(df['线索提交时间'])
    
    # 获取日期范围
    min_date, max_date, default_start = get_date_range(df, '线索提交时间')
    
    # 如果没有指定日期，使用默认值
    if not end_date:
        end_date = max_date
    if not start_date:
        start_date = default_start
    
    # 定义种植和正畸的推广账号
    plant_accounts = ['********', '********', '********','********','********']
    ortho_accounts = ['********', '********']
    
    # 筛选日期范围内的数据
    df = df[(df['线索提交时间'].dt.strftime('%Y-%m-%d') >= start_date) & 
           (df['线索提交时间'].dt.strftime('%Y-%m-%d') <= end_date)]
    
    # 在筛选日期后再进行所有的数据处理
    plant_total = len(df[df['推广账号ID'].isin(plant_accounts)]['电话'].unique())
    ortho_total = len(df[df['推广账号ID'].isin(ortho_accounts)]['电话'].unique())
    
    # 统计线索状态分布
    status_data = df['线索状态'].value_counts().to_dict()
    status_series = [{'name': k, 'value': v} for k, v in status_data.items()]
    
    # 统计线索标签分布
    label_data = df['线索标签'].value_counts().to_dict()
    label_series = [{'name': k, 'value': v} for k, v in label_data.items()]
    
    # 统计备注分布
    note_data = df['Unnamed: 26'].value_counts().to_dict()
    note_series = [{'name': k, 'value': v} for k, v in note_data.items()]
    
    # 使用 groupby 统计每个归属人的备注分布和有效率
    owner_stats = df.groupby('归属人').agg({
        'Unnamed: 26': lambda x: x.value_counts().to_dict()
    })
    owner_stats['total'] = df.groupby('归属人').size()
    
    owner_note_stats = []
    for owner, row in owner_stats.iterrows():
        total = row['total']
        valid_count = row['Unnamed: 26'].get('有效', 0)
        valid_rate = round(valid_count / total * 100, 2) if total > 0 else 0
        owner_note_stats.append({
            'owner': owner,
            'total': total,
            'notes': row['Unnamed: 26'],
            'valid_rate': valid_rate
        })
    
    # 按电话总数降序排序
    owner_note_stats = sorted(owner_note_stats, key=lambda x: x['total'], reverse=True)
    
    # 使用 groupby 统计每个推广链接的备注分布和有效率
    link_stats = df.groupby('推广链接').agg({
        'Unnamed: 26': lambda x: x.value_counts().to_dict()
    })
    link_stats['total'] = df.groupby('推广链接').size()
    
    link_note_stats = []
    for link, row in link_stats.iterrows():
        total = row['total']
        valid_count = row['Unnamed: 26'].get('有效', 0)
        valid_rate = round(valid_count / total * 100, 2) if total > 0 else 0
        link_note_stats.append({
            'link': link,
            'total': total,
            'notes': row['Unnamed: 26'],
            'valid_rate': valid_rate
        })
    
    # 按电话总数降序排序
    link_note_stats = sorted(link_note_stats, key=lambda x: x['total'], reverse=True)
    
    # 统计归属人的电话数量
    owner_data = df['归属人'].value_counts()
    owner_names = owner_data.index.tolist()
    owner_values = owner_data.values.tolist()
    
    # 统计账户数据
    account_stats = df.groupby('推广账号ID').agg({
        'Unnamed: 26': lambda x: x.value_counts().to_dict()
    })
    account_stats['total'] = df.groupby('推广账号ID').size()
    
    account_note_stats = []
    for account, row in account_stats.iterrows():
        total = row['total']
        valid_count = row['Unnamed: 26'].get('有效', 0)
        # 计算重单数量和重单率
        duplicate_count = row['Unnamed: 26'].get('重单', 0)
        duplicate_rate = round(duplicate_count / total * 100, 2) if total > 0 else 0
        valid_rate = round(valid_count / total * 100, 2) if total > 0 else 0
        account_note_stats.append({
            'account': account,
            'total': total,
            'notes': row['Unnamed: 26'],
            'valid_rate': valid_rate,
            'duplicate_count': duplicate_count,
            'duplicate_rate': duplicate_rate
        })
    
    # 按电话总数降序排序
    account_note_stats = sorted(account_note_stats, key=lambda x: x['total'], reverse=True)
    
    # 统计计划数据
    plan_stats = df.groupby('推广计划').agg({
        'Unnamed: 26': lambda x: x.value_counts().to_dict()
    })
    plan_stats['total'] = df.groupby('推广计划').size()
    
    plan_note_stats = []
    for plan, row in plan_stats.iterrows():
        total = row['total']
        valid_count = row['Unnamed: 26'].get('有效', 0)
        # 计算重单数量和重单率
        duplicate_count = row['Unnamed: 26'].get('重单', 0)
        duplicate_rate = round(duplicate_count / total * 100, 2) if total > 0 else 0
        valid_rate = round(valid_count / total * 100, 2) if total > 0 else 0
        plan_note_stats.append({
            'plan': plan,
            'total': total,
            'notes': row['Unnamed: 26'],
            'valid_rate': valid_rate,
            'duplicate_count': duplicate_count,
            'duplicate_rate': duplicate_rate
        })
    
    # 按电话总数降序排序
    plan_note_stats = sorted(plan_note_stats, key=lambda x: x['total'], reverse=True)
    
    # 统计广告数据
    ad_stats = df.groupby('广告').agg({
        'Unnamed: 26': lambda x: x.value_counts().to_dict()
    })
    ad_stats['total'] = df.groupby('广告').size()
    
    ad_note_stats = []
    for ad, row in ad_stats.iterrows():
        total = row['total']
        valid_count = row['Unnamed: 26'].get('有效', 0)
        # 计算重单数量和重单率
        duplicate_count = row['Unnamed: 26'].get('重单', 0)
        duplicate_rate = round(duplicate_count / total * 100, 2) if total > 0 else 0
        valid_rate = round(valid_count / total * 100, 2) if total > 0 else 0
        ad_note_stats.append({
            'ad': ad,
            'total': total,
            'notes': row['Unnamed: 26'],
            'valid_rate': valid_rate,
            'duplicate_count': duplicate_count,
            'duplicate_rate': duplicate_rate
        })
    
    # 按电话总数降序排序
    ad_note_stats = sorted(ad_note_stats, key=lambda x: x['total'], reverse=True)
    
    return render_template('friend_circle.html',
                         plant_total=plant_total,
                         ortho_total=ortho_total,
                         status_series=status_series,
                         label_series=label_series,
                         note_series=note_series,
                         owner_note_stats=owner_note_stats,
                         link_note_stats=link_note_stats,
                         owner_names=owner_names,
                         owner_values=owner_values,
                         account_note_stats=account_note_stats,
                         plan_note_stats=plan_note_stats,
                         ad_note_stats=ad_note_stats,
                         min_date=min_date,
                         max_date=max_date,
                         start_date=start_date or min_date,
                         end_date=end_date or max_date)

#外链数据
@app.route('/wailian')
def wailian():
    # 获取查询参数中的开始和结束时间
    end_date = request.args.get('end_date')
    start_date = request.args.get('start_date')
    
    # 读取数据文件
    df = pd.read_csv('wailian/2025-01-06 16 32 41.csv', encoding='gbk')
    df['创建时间'] = pd.to_datetime(df['创建时间'])
    
    # 获取日期范围
    min_date, max_date, default_start = get_date_range(df, '创建时间')
    
    # 如果没有指定日期，使用默认值
    if not end_date:
        end_date = max_date
    if not start_date:
        start_date = default_start
    
    # 筛选日期范围内的数据
    df = df[(df['创建时间'].dt.strftime('%Y-%m-%d') >= start_date) & 
           (df['创建时间'].dt.strftime('%Y-%m-%d') <= end_date)]
    
    # 在筛选日期后再进行所有的数据处理
    total_phones = len(df)
    
    # 统计客服备注分布
    note_data = df['客服备注'].value_counts().to_dict()
    note_series = [{'name': k, 'value': v} for k, v in note_data.items()]
    
    # 统计建档人的手机号数量
    recorder_data = df['建档人'].value_counts()
    recorder_names = recorder_data.index.tolist()
    recorder_values = recorder_data.values.tolist()
    
    # 使用 groupby 统计每个建档人的备注分布和有效率
    recorder_stats = df.groupby('建档人').agg({
        '客服备注': lambda x: x.value_counts().to_dict()
    })
    recorder_stats['total'] = df.groupby('建档人').size()
    
    recorder_note_stats = []
    for recorder, row in recorder_stats.iterrows():
        total = row['total']
        valid_count = row['客服备注'].get('有效', 0)
        valid_rate = round(valid_count / total * 100, 2) if total > 0 else 0
        recorder_note_stats.append({
            'recorder': recorder,
            'total': total,
            'notes': row['客服备注'],
            'valid_rate': valid_rate
        })
    
    # 按手机号总数降序排序
    recorder_note_stats = sorted(recorder_note_stats, key=lambda x: x['total'], reverse=True)
    
    return render_template('wailian.html',
                         total_phones=total_phones,
                         min_date=min_date,
                         max_date=max_date,
                         start_date=start_date or min_date,
                         end_date=end_date or max_date,
                         note_series=note_series,
                         recorder_names=recorder_names,
                         recorder_values=recorder_values,
                         recorder_note_stats=recorder_note_stats)
#快手数据
@app.route('/ks')
def ks():
    # 获取查询参数中的开始和结束时间
    end_date = request.args.get('end_date')
    start_date = request.args.get('start_date')
    
    # 读取快手数据文件
    df = pd.read_excel('ks/2025-01-07 12 08 59.xlsx')
    df['收集日期'] = pd.to_datetime(df['收集日期'])
    
    # 获取日期范围
    min_date, max_date, default_start = get_date_range(df, '收集日期')
    
    # 如果没有指定日期，使用默认值
    if not end_date:
        end_date = max_date
    if not start_date:
        start_date = default_start
    
    # 筛选日期范围内的数据
    df = df[(df['收集日期'].dt.strftime('%Y-%m-%d') >= start_date) & 
           (df['收集日期'].dt.strftime('%Y-%m-%d') <= end_date)]
    
    # 在筛选日期后再进行所有的数据处理
    # 计算电话总数 (只统计电话列的非空值)
    total_phones = df['电话'].count()
    
    # 统计备注列的数据
    note_counts = df['备注'].value_counts()
    note_series = [{'name': name, 'value': int(value)} for name, value in note_counts.items()]
    
    # 统计建档人的电话数量
    recorder_counts = df['建档人'].value_counts()
    recorder_names = recorder_counts.index.tolist()
    recorder_values = recorder_counts.values.tolist()
    
    # 1. 建档人统计
    recorder_stats = []
    for name, group in df.groupby('建档人'):
        total = len(group)
        note_counts = group['备注'].value_counts().to_dict()
        valid_count = group[group['备注'] == '有效'].shape[0]
        valid_rate = round(valid_count / total * 100, 1) if total > 0 else 0
        
        recorder_stats.append({
            'name': name,
            'total': total,
            'notes': note_counts,
            'valid_rate': valid_rate
        })
    
    # 按总电话数降序排序
    recorder_stats.sort(key=lambda x: x['total'], reverse=True)
    
    # 获取所有可能的备注类型
    all_notes = sorted(df['备注'].unique())
    
    # 2. 来源页地址统计
    def process_url(url):
        if pd.isna(url):  # 处理空值
            return ''
        if '&' in str(url):
            parts = str(url).split('&')
            if len(parts) > 1:
                return '&'.join(parts[:2])
        return str(url)
    
    # 添加处理后的URL列
    df['processed_url'] = df['来源页地址'].apply(process_url)
    
    # 使用处理后的URL进行分组统计
    source_stats = []
    for url, group in df.groupby('processed_url'):
        if not url:  # 跳过空URL
            continue
        total = len(group)
        note_counts = group['备注'].value_counts().to_dict()
        valid_count = group[group['备注'] == '有效'].shape[0]
        valid_rate = round(valid_count / total * 100, 1) if total > 0 else 0
        
        # 获取该组的原始URL（取第一个）
        original_url = group['来源页地址'].iloc[0]
        
        source_stats.append({
            'url': original_url,
            'display_url': url,
            'total': total,
            'notes': note_counts,
            'valid_rate': valid_rate
        })
    
    # 按总电话数降序排序
    source_stats.sort(key=lambda x: x['total'], reverse=True)
    
    # 3. 视频URL统计
    video_stats = []
    for url, group in df.groupby('视频URL'):
        if pd.isna(url):  # 跳过空URL
            continue
        total = len(group)
        note_counts = group['备注'].value_counts().to_dict()
        valid_count = group[group['备注'] == '有效'].shape[0]
        valid_rate = round(valid_count / total * 100, 1) if total > 0 else 0
        
        video_stats.append({
            'url': url,
            'total': total,
            'notes': note_counts,
            'valid_rate': valid_rate
        })
    
    # 按总电话数降序排序
    video_stats.sort(key=lambda x: x['total'], reverse=True)
    
    return render_template('ks.html',
                         total_phones=total_phones,
                         note_series=note_series,
                         recorder_names=recorder_names,
                         recorder_values=recorder_values,
                         recorder_stats=recorder_stats,
                         source_stats=source_stats,
                         video_stats=video_stats,
                         all_notes=all_notes,
                         min_date=min_date,
                         max_date=max_date,
                         start_date=start_date,
                         end_date=end_date)

# @app.route('/fenlei')
# def fenlei():
#     return render_template('fenlei.html')

# @app.route('/classify', methods=['POST'])
# def classify():
#     if 'file' not in request.files:
#         return jsonify({'error': 'No file part'})
    
#     file = request.files['file']
    
#     if file.filename == '':
#         return jsonify({'error': 'No selected file'})
    
#     if file:
#         # Create the uploads directory if it does not exist
#         if not os.path.exists('uploads'):
#             os.makedirs('uploads')
        
#         filename = os.path.join('uploads', file.filename)
#         file.save(filename)
        
#         # 在后台线程中运行分类任务
#         thread = threading.Thread(target=classify_data, args=(filename,))
#         thread.start()
        
#         return jsonify({'message': 'Classification started. You will be notified when it is done.'})

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)