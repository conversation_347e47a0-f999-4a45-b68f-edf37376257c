#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

print("=== 网电咨询师统计应用启动器 ===")
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")

try:
    print("正在导入Flask...")
    from flask import Flask, render_template, request, jsonify
    print("✓ Flask导入成功")
    
    print("正在导入pandas...")
    import pandas as pd
    print("✓ pandas导入成功")
    
    print("正在导入数据获取函数...")
    from 网电咨询师来院统计 import get_all_wdzx_consultants_stats_to_excel
    print("✓ 数据获取函数导入成功")
    
    print("正在创建Flask应用...")
    app = Flask(__name__)
    print("✓ Flask应用创建成功")
    
    @app.route('/')
    def index():
        return """
        <html>
        <head>
            <title>网电咨询师统计</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                h1 { color: #333; }
                .btn { 
                    display: inline-block; 
                    padding: 10px 20px; 
                    margin: 10px; 
                    background: #007bff; 
                    color: white; 
                    text-decoration: none; 
                    border-radius: 5px; 
                }
                .btn:hover { background: #0056b3; }
            </style>
        </head>
        <body>
            <h1>🌐 网电咨询师来院数据统计系统</h1>
            <p>系统运行正常！</p>
            <div>
                <a href="/dashboard" class="btn">📊 可视化大屏</a>
                <a href="/test" class="btn">🔧 API测试</a>
                <a href="/api/test" class="btn">⚡ 快速测试</a>
            </div>
        </body>
        </html>
        """
    
    @app.route('/dashboard')
    def dashboard():
        try:
            return render_template('wdzx_dashboard_fullscreen.html',
                                 start_date='2025-09-01',
                                 end_date='2025-09-05')
        except Exception as e:
            return f"""
            <html>
            <body>
                <h1>模板加载失败</h1>
                <p>错误: {e}</p>
                <p><a href="/">返回首页</a></p>
            </body>
            </html>
            """
    
    @app.route('/test')
    def test_page():
        return """
        <html>
        <head><title>API测试</title></head>
        <body>
            <h1>网电咨询师API测试</h1>
            <button onclick="testAPI()">测试API</button>
            <div id="result" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc;"></div>
            <script>
                async function testAPI() {
                    const resultDiv = document.getElementById('result');
                    resultDiv.innerHTML = '正在测试...';
                    try {
                        const response = await fetch('/api/test');
                        const data = await response.json();
                        resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    } catch (error) {
                        resultDiv.innerHTML = '错误: ' + error.message;
                    }
                }
            </script>
        </body>
        </html>
        """
    
    @app.route('/api/test')
    def api_test():
        try:
            return jsonify({
                "status": "success",
                "message": "网电咨询师API测试成功",
                "function_available": callable(get_all_wdzx_consultants_stats_to_excel),
                "timestamp": pd.Timestamp.now().isoformat()
            })
        except Exception as e:
            return jsonify({
                "status": "error",
                "message": str(e)
            })
    
    print("=== 启动Web服务器 ===")
    print("访问地址: http://localhost:5006")
    print("大屏地址: http://localhost:5006/dashboard")
    print("按 Ctrl+C 停止服务器")
    print("=" * 40)
    
    app.run(debug=True, host='0.0.0.0', port=5006)
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    input("按回车键退出...")
