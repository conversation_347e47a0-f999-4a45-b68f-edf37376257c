# 年份月份筛选功能实现说明

## 功能概述

已成功实现在index.html中选择对应的年份、月份、渠道，筛选显示对应数据的功能。

## 实现的主要功能

### 1. 年份选择器
- 在页面顶部添加了年份选择按钮（2024年、2025年等）
- 支持动态切换年份
- 年份按钮有明显的视觉反馈（蓝色主题）

### 2. 月份选择器
- 每个年份下显示对应的月份文件
- 月份按年份分组显示
- 切换年份时自动显示对应年份的月份选项

### 3. 渠道选择器
- 保持原有的渠道选择功能
- 支持抖音、朋友圈、百度、快手、高德、美团、合计等渠道

### 4. 数据筛选
- 根据选择的年份、月份、渠道动态加载对应数据
- 消费数据支持年份筛选（从每月消費.xlsx中根据年份列筛选）
- 投产比计算基于对应年份月份的数据

## 技术实现

### 后端修改

1. **get_excel_files()函数**
   - 修改为按年份目录组织文件
   - 返回年份和对应月份文件的字典结构

2. **get_channel_data()函数**
   - 添加year参数支持
   - 文件路径修改为：`lib/{year}/{filename}`
   - 消费数据查询增加年份条件

3. **API路由**
   - 修改为：`/api/data/<year>/<filename>`
   - 支持年份参数传递

### 前端修改

1. **HTML结构**
   - 添加年份选择器(.year-tabs)
   - 月份按年份分组(.month-group)
   - 添加对应的CSS样式

2. **JavaScript功能**
   - 添加年份切换逻辑
   - 修改API调用路径
   - 实现年份月份联动效果

## 数据组织结构

```
lib/
├── 2024/
│   ├── 8月.xlsx
│   ├── 9月.xlsx
│   ├── 10月.xlsx
│   ├── 11月.xlsx
│   └── 12月.xlsx
└── 2025/
    ├── 1月.xlsx
    ├── 2月.xlsx
    ├── 3月.xlsx
    ├── 4月.xlsx
    ├── 5月.xlsx
    ├── 6月.xlsx
    └── 7月.xlsx
```

## 消费数据结构

每月消費.xlsx文件包含年份列：
- 年份：2024, 2025等
- 月份：对应的月份名称
- 渠道：各个推广渠道
- 消费：对应的消费金额

## 使用方法

1. 打开首页
2. 选择年份（默认选择第一个年份）
3. 选择月份（默认选择该年份的第一个月份）
4. 选择渠道（默认选择抖音）
5. 系统自动加载并显示对应的数据分析结果

## 测试验证

已验证功能正常工作：
- ✅ 年份切换正常
- ✅ 月份切换正常  
- ✅ 渠道切换正常
- ✅ 数据正确加载
- ✅ API路径正确
- ✅ 消费数据按年份筛选正常

## 默认显示最新数据

### ✅ 新增功能
- **默认选择最新年份**：系统启动时自动选择最新的年份（2025年）
- **默认选择最新月份**：在最新年份中自动选择最新的月份（8月）
- **默认渠道抖音**：保持默认显示抖音渠道数据
- **智能排序**：年份按降序排列，月份按时间顺序降序排列

### 🔧 技术实现

**后端排序逻辑：**
- 年份按数值降序排列：2025 > 2024
- 月份按时间顺序降序排列：8月 > 7月 > 6月 > ... > 1月
- 在index路由中传递latest_year和latest_file给模板

**前端默认选择：**
- JavaScript中的initDefaults()函数自动选择最新年份和月份
- 模板中使用latest_year和latest_file标记默认active状态
- 页面加载时自动请求最新数据

## 示例数据对比

- **2024年8月抖音数据**：总记录数 2682，到院总人数 197，成交总金额 566316
- **2025年1月抖音数据**：总记录数 1589，到院总人数 118，成交总金额 441126
- **2025年8月抖音数据**：最新数据（默认显示）

### 🎯 最终效果

现在打开index.html页面时，会：
1. **默认显示2025年**（最新年份）
2. **默认显示8月**（最新月份）
3. **默认显示抖音数据**（默认渠道）
4. **自动加载最新时间的抖音大屏数据**

用户可以随时切换到其他年份、月份、渠道查看历史数据，但默认总是显示最新的数据。
