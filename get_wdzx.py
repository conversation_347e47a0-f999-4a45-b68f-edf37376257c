#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取现场咨询师列表
从内部系统获取最新的现场咨询师信息并格式化输出
"""

import requests
try:
    from bs4 import BeautifulSoup
except ImportError:
    print("需要安装BeautifulSoup4: pip install beautifulsoup4")
    BeautifulSoup = None


def get_consultants_list():
    """
    获取网电咨询师列表

    Returns:
        dict: 现场咨询师字典，格式为 {"姓名(工号)": "ID"}
    """

    # 请求URL
    url = "http://************/Reservation/ToHospital/Index"

    # 请求头
    headers = {
        "Accept": "text/html, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Origin": "http://************",
        "Pragma": "no-cache",
        "Referer": "http://************/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    # Cookie
    cookies = {
        "************80_AdminContext_UserName": "ZN009",
        "************80_AdminContext_UserId": "2ADB7F12A38F4D9680997C4EB0C44496",
        "************80_AdminContext_UserToken": "AD5B2BE51FEB45E1840FB33E008AA554"
    }

    # 请求数据
    data = {
        "DatetimeRegStart": "2025-08-18",
        "DatetimeRegEnd": "2025-08-18",
        "TempDatetimeRegStart": "",
        "TempDatetimeRegEnd": "",
        "CustName": "",
        "Phone": "",
        "CustStatus": "",
        "IsDeal": "",
        "IsBookCust": "",
        "SectionId": "",
        "Medias": "",
        "MediasMenus": "",
        "Ptype1": "",
        "Ptype2": "",
        "Ptype3": "",
        "ProductTypeName1s": "",
        "ProductTypeName2s": "",
        "ProductTypeNames": "",
        "TmpCustRegType": "",
        "TmpCustRegTypeMenus": "",
        "IsLab": "",
        "CustLabelId": "",
        "CustLabelMenus": "",
        "NoCustLabelId": "",
        "NoCustLabelMenus": "",
        "BookEmpId": "",
        "FieldConsultantId": "",
        "TempCreateBy": "",
        "PlanRecallEmp": "",
        "IsHospSecond": "",
        "Remark": "",
        "Province": "",
        "City": "",
        "Area": "",
        "QQ": "",
        "WeiXinNo": "",
        "GuestId": "",
        "CustCardno": "",
        "Sex": "",
        "TempRecommendEmpId": "",
        "AttentionRemark": "",
        "IsDealCust": "",
        "pageSize": "21",
        "pageCurrent": "1",
        "iscompay": "1",
        "CrossRelation": "",
        "EmployeeToChannel": "",
        "orderField": "",
        "orderDirection": "",
        "total": ""
    }

    try:
        # 发送POST请求
        response = requests.post(url, headers=headers, cookies=cookies, data=data, verify=False)
        response.raise_for_status()

        # 解析HTML
        html_content = response.text

        # 使用BeautifulSoup解析
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找现场咨询师的select元素
        consultant_select = soup.find('select', {'id': 'BookEmpId'})

        if not consultant_select:
            print("未找到现场咨询师选择框")
            return {}

        # 提取所有option元素
        options = consultant_select.find_all('option')

        consultants = {}

        for option in options:
            value = option.get('value', '').strip()
            text = option.get_text().strip()

            # 跳过空值和"请选择"选项
            if not value or not text or text == "请选择":
                continue

            consultants[text] = value

        return consultants

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return {}
    except Exception as e:
        print(f"解析失败: {e}")
        return {}


def format_consultants_dict(consultants):
    """
    格式化现场咨询师字典为Python代码格式

    Args:
        consultants (dict): 现场咨询师字典

    Returns:
        str: 格式化的Python字典代码
    """
    if not consultants:
        return "consultants = {}"

    lines = ["consultants = {"]

    # 按姓名排序
    sorted_items = sorted(consultants.items())

    for name, consultant_id in sorted_items:
        lines.append(f'    "{name}": "{consultant_id}",')

    lines.append("}")

    return "\n".join(lines)


if __name__ == "__main__":
    result=get_consultants_list()
    print(format_consultants_dict(result))