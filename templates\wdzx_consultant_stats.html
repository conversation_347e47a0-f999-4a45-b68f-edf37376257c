<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网电咨询师来院数据统计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 32px;
            color: #333;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .subtitle {
            color: #666;
            font-size: 16px;
        }

        .controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-group label {
            font-weight: bold;
            color: #333;
        }

        .control-group input {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .control-group input:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            padding: 10px 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        .btn-secondary:hover {
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .navigation {
            text-align: center;
            margin-bottom: 30px;
        }

        .nav-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stats-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .stat-card .label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .stat-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-size: 18px;
        }

        .error {
            text-align: center;
            padding: 40px;
            color: #dc3545;
            font-size: 18px;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .control-group {
                justify-content: space-between;
            }

            .nav-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">网电咨询师来院数据统计</h1>
            <p class="subtitle">数据分析与可视化平台</p>
        </div>

        <div class="navigation">
            <div class="nav-links">
                <a href="/dashboard" class="btn btn-secondary">📊 可视化大屏</a>
                <a href="/test" class="btn">🔧 API测试</a>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label for="start-date">开始日期:</label>
                <input type="date" id="start-date" value="{{ start_date }}">
            </div>
            <div class="control-group">
                <label for="end-date">结束日期:</label>
                <input type="date" id="end-date" value="{{ end_date }}">
            </div>
            <button class="btn" onclick="loadData()">查询数据</button>
        </div>

        <div id="stats-container">
            <div class="loading">请选择日期范围并点击查询数据</div>
        </div>
    </div>

    <script>
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里添加初始化逻辑
        });

        // 加载数据
        async function loadData() {
            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;
            const container = document.getElementById('stats-container');

            if (!startDate || !endDate) {
                alert('请选择开始日期和结束日期');
                return;
            }

            container.innerHTML = '<div class="loading">正在加载数据...</div>';

            try {
                const response = await fetch(`/api/stats?start_date=${startDate}&end_date=${endDate}`);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || '获取数据失败');
                }

                displayStats(data);
            } catch (error) {
                console.error('加载数据失败:', error);
                container.innerHTML = `<div class="error">加载数据失败: ${error.message}</div>`;
            }
        }

        // 显示统计数据
        function displayStats(data) {
            const container = document.getElementById('stats-container');
            
            let html = `
                <div class="stats-summary">
                    <div class="stat-card">
                        <div class="label">总开单业绩</div>
                        <div class="value">${data.summary.total_performance.toLocaleString()}</div>
                    </div>
                    <div class="stat-card">
                        <div class="label">总人数</div>
                        <div class="value">${data.summary.total_people.toLocaleString()}</div>
                    </div>
                    <div class="stat-card">
                        <div class="label">成交人数</div>
                        <div class="value">${data.summary.total_deal.toLocaleString()}</div>
                    </div>
                    <div class="stat-card">
                        <div class="label">成交率</div>
                        <div class="value">${data.summary.deal_rate}%</div>
                    </div>
                    <div class="stat-card">
                        <div class="label">客单价</div>
                        <div class="value">${data.summary.avg_price.toLocaleString()}</div>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <p style="color: #666; margin-bottom: 20px;">
                        共统计 ${data.consultants.length} 位网电咨询师的数据
                    </p>
                    <a href="/dashboard" class="btn btn-secondary">查看详细可视化大屏 →</a>
                </div>
            `;

            container.innerHTML = html;
        }
    </script>
</body>
</html>
