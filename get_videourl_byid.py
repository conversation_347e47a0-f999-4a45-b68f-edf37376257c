import requests

def get_video_info(csrf_token):
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        "Content-Type": "application/json",
        "Origin": "https://business.oceanengine.com",
        "Pragma": "no-cache",
        "Referer": f"https://business.oceanengine.com/site/asset/material_center/management/video?cc_id={1807160717110459}",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-csrftoken": csrf_token,
        "x-csrf-token": '',
    }
    cookies={
        "csrftoken": csrf_token,
        "sessionid": '1b9a22c0421bd13ec0b5944520a14432',
        "uid_tt": 'd9e661fca3791b4e4749c271282c6b8d',
        "sid_tt":'1b9a22c0421bd13ec0b5944520a14432'
    }
    url = "https://business.oceanengine.com/nbs/api/bm/asset/video/get_video_info"
    data = '{"vids":["v0d033g10000d1rh8c7og65mjl9a1fk0"]}'.encode('unicode_escape')
    
    response = requests.post(url, headers=headers, cookies=cookies, data=data)

    
    print(response.text)
    print(response)
    return response

# Example usage
video_info = get_video_info('udJPzdVe_Py8fl4vT0NGyQcZ')

print(video_info)