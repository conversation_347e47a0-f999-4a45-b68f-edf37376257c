<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音飞鱼数据分析</title>
    <script>
        // 定义多个 CDN 源
        const CDN_URLS = [
            // 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js',
            // 'https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js',
            // 'https://cdn.bootcdn.net/ajax/libs/echarts/5.4.3/echarts.min.js',
            '/static/echarts.min.js'  // 本地备份
        ];

        // 依次尝试加载 CDN
        function loadECharts(urls) {
            if (urls.length === 0) {
                console.error('所有 CDN 加载失败');
                return;
            }

            const script = document.createElement('script');
            script.src = urls[0];
            script.onerror = () => {
                console.warn(`CDN ${urls[0]} 加载失败，尝试下一个`);
                loadECharts(urls.slice(1));
            };
            document.head.appendChild(script);
        }

        loadECharts(CDN_URLS);
    </script>
    <style>
        :root {
            --primary-color: #1890ff;
            --primary-dark: #096dd9;
            --text-light: #ffffff;
            --card-bg: rgba(255, 255, 255, 0.98);
        }
        
        body {
            margin: 0;
            padding: 15px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
            color: var(--text-light);
            height: 100vh; /* 使用视窗高度 */
            overflow: hidden; /* 防止滚动 */
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }
        
        .header {
            text-align: center;
            padding: 10px;
            flex-shrink: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .content-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
            overflow-y: auto; /* 允许内容滚动 */
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            gap: 15px;
            flex-shrink: 0;
        }
        
        .stat-box {
            background: var(--card-bg);
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            flex: 1;
            text-align: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .stat-box:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .stat-box h3 {
            color: var(--primary-dark);
            margin: 0;
            font-size: 14px;
            font-weight: 600;
        }
        
        .stat-box p {
            font-size: 36px;
            font-weight: 800;
            margin: 5px 0 0;
            color: #ff4d4f;
            text-shadow: 0 2px 4px rgba(255, 77, 79, 0.2);
        }
        
        .charts-row {
            display: flex;
            gap: 15px;
            min-height: 400px;
        }
        
        .chart-container {
            background: var(--card-bg);
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            display: flex;
            flex-direction: column;
        }
        
        .charts-row .chart-container {
            flex: 1;  /* 让三个饼图容器平均分配空间 */
        }
        
        .chart {
            height: 350px;  /* 稍微减小饼图高度以确保完整显示 */
            width: 100%;
        }
        
        .bottom-chart {
            margin-top: 15px;
            height: 500px; /* 为柱状图设置更大的高度 */
        }
        
        @media (max-width: 768px) {
            body {
                height: 100%;
                overflow: auto;
            }
            
            .content-wrapper {
                gap: 10px;
            }
            
            .charts-row {
                flex-direction: column;
            }
            
            .chart {
                height: 300px;
            }
            
            .bottom-chart {
                height: 400px;
            }
            
            .stat-box h3 {
                font-size: 12px;
            }
            
            .stat-box p {
                font-size: 28px;
            }
        }
        .table-wrapper {
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            font-size: 14px;
        }
        
        .data-table th,
        .data-table td {
            padding: 8px;
            text-align: center;
            border: 1px solid #e8e8e8;
            color: #333;
        }
        
        .data-table th {
            background: #f5f5f5;
            font-weight: 600;
        }
        
        .data-table a {
            color: var(--primary-color);
            text-decoration: none;
        }
        
        .data-table a:hover {
            text-decoration: underline;
        }
        
        .date-filter {
            margin: 20px 0;
            background: var(--card-bg);
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .date-filter-form {
            display: flex;
            gap: 20px;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .date-input-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .date-input-group label {
            color: var(--primary-dark);
            font-weight: 600;
        }
        
        .date-input-group input {
            padding: 8px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .filter-btn {
            padding: 8px 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-btn:hover {
            background: var(--primary-dark);
        }
        
        @media (max-width: 768px) {
            .date-filter-form {
                flex-direction: column;
                align-items: stretch;
            }
            
            .date-input-group {
                flex-direction: column;
                align-items: stretch;
            }
        }
        
        .nav-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .nav-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
            backdrop-filter: blur(5px);
        }
        
        .nav-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .nav-btn.active {
            background-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        @media (max-width: 768px) {
            .nav-buttons {
                flex-direction: column;
                width: 100%;
            }
            
            .nav-btn {
                width: 100%;
                text-align: center;
            }
        }
        
        .float-nav {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .float-btn {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: #1890ff;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .float-btn:hover {
            transform: translateX(-5px);
            background: #1890ff;
            color: white;
            box-shadow: 0 2px 15px rgba(24, 144, 255, 0.3);
        }

        .nav-icon {
            font-style: normal;
        }

        @media (max-width: 768px) {
            .float-nav {
                right: 10px;
            }
            
            .float-btn {
                width: 40px;
                height: 40px;
                font-size: 12px;
            }
        }

        .tab-header {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .tab-btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            background: #1890ff;
            color: white;
            border-color: #1890ff;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>抖音飞鱼数据分析</h1>
        <div class="nav-buttons">
            <a href="/feiyu" class="nav-btn active">抖音飞鱼数据分析</a>
            <a href="/friend_circle" class="nav-btn">朋友圈数据分析</a>
            <a href="/wailian" class="nav-btn">外链数据分析</a>
            <a href="/ks" class="nav-btn">快手数据分析</a>
        </div>
    </div>
    
    <div class="date-filter">
        <form id="dateFilterForm" class="date-filter-form">
            <div class="date-input-group">
                <label for="start_date">开始时间：</label>
                <input type="date" id="start_date" name="start_date" 
                       value="{{ start_date }}" 
                       min="{{ min_date }}" 
                       max="{{ max_date }}">
            </div>
            <div class="date-input-group">
                <label for="end_date">结束时间：</label>
                <input type="date" id="end_date" name="end_date" 
                       value="{{ end_date }}" 
                       min="{{ min_date }}" 
                       max="{{ max_date }}">
            </div>
            <button type="submit" class="filter-btn">筛选</button>
        </form>
    </div>
    
    <div class="content-wrapper">
        <div class="stats">
            <div class="stat-box">
                <h3>电话总数</h3>
                <p>{{ total_phones }}</p>
            </div>
            <div class="stat-box">
                <h3>非虚拟线索总数</h3>
                <p>{{ valid_leads }}</p>
            </div>
        </div>
        
        <div class="charts-row">
            <div class="chart-container">
                <div id="labelChart" class="chart"></div>
            </div>
            <div class="chart-container">
                <div id="stageChart" class="chart"></div>
            </div>
            <div class="chart-container">
                <div id="noteChart" class="chart"></div>
            </div>
        </div>
        <div class="charts-row">
            <div class="chart-container">
                <div class="table-wrapper">
                    <h3>所属人备注统计</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>所属人</th>
                                <th>总电话数</th>
                                {% for note in note_series %}
                                <th>{{ note.name }}</th>
                                {% endfor %}
                                <th>有效率</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for stat in owner_note_stats %}
                            <tr>
                                <td>{{ stat.owner }}</td>
                                <td>{{ stat.total }}</td>
                                {% for note in note_series %}
                                <td>{{ stat.notes.get(note.name, 0) }}</td>
                                {% endfor %}
                                <td>{{ stat.valid_rate }}%</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="table-wrapper">
                <h3>落地页备注统计</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>落地页链接</th>
                            <th>总电话数</th>
                            {% for note in note_series %}
                            <th>{{ note.name }}</th>
                            {% endfor %}
                            <th>有效率</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stat in landing_note_stats %}
                        <tr>
                            <td><a href="{{ stat.url }}" target="_blank">{{ stat.url }}</a></td>
                            <td>{{ stat.total }}</td>
                            {% for note in note_series %}
                            <td>{{ stat.notes.get(note.name, 0) }}</td>
                            {% endfor %}
                            <td>{{ stat.valid_rate }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="table-wrapper">
                <h3>视频ID备注统计</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>视频ID</th>
                            <th>总电话数</th>
                            {% for note in note_series %}
                            <th>{{ note.name }}</th>
                            {% endfor %}
                            <th>有效率</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stat in video_note_stats %}
                        <tr>
                            <td>{{ stat.video_id }}</td>
                            <td>{{ stat.total }}</td>
                            {% for note in note_series %}
                            <td>{{ stat.notes.get(note.name, 0) }}</td>
                            {% endfor %}
                            <td>{{ stat.valid_rate }}%</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="chart-container">
            <div class="tab-header">
                <button class="tab-btn active" data-tab="ad-stats">广告ID统计</button>
                <button class="tab-btn" data-tab="account-stats">账户统计</button>
            </div>
            
            <div class="table-wrapper tab-content active" id="ad-stats">
                <h3>广告ID统计</h3>
                <div class="account-filter" style="margin-bottom: 15px;">
                    <label for="account-select" style="color: #333; margin-right: 10px;">选择账户：</label>
                    <select id="account-select" style="padding: 5px; border-radius: 4px; border: 1px solid #d9d9d9;">
                        <option value="">全部账户</option>
                        {% for account in account_list %}
                        <option value="{{ account }}">{{ account }}</option>
                        {% endfor %}
                    </select>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>广告ID</th>
                            <th>账户</th>
                            <th>总消耗</th>
                            <th>有效数</th>
                            <th>有效成本</th>
                            <th>到院数</th>
                            <th>到院成本</th>
                            <th>成交业绩</th>
                            <th>投产比</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stat in ad_stats %}
                        <tr>
                            <td>{{ stat.ad_id }}</td>
                            <td>{{ stat.account }}</td>
                            <td>{{ stat.total_cost }}</td>
                            <td>{{ stat.conversion_count }}</td>
                            <td>{{ stat.conversion_cost }}</td>
                            <td>{{ stat.visit_count }}</td>
                            <td>{{ stat.visit_cost }}</td>
                            <td>{{ stat.total_amount }}</td>
                            <td>{{ stat.roi }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <div class="table-wrapper tab-content" id="account-stats">
                <h3>账户统计</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>账户</th>
                            <th>总消耗</th>
                            <th>有效数</th>
                            <th>有效成本</th>
                            <th>到院数</th>
                            <th>到院成本</th>
                            <th>成交业绩</th>
                            <th>投产比</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for stat in account_stats %}
                        <tr>
                            <td>{{ stat.account }}</td>
                            <td>{{ stat.total_cost }}</td>
                            <td>{{ stat.conversion_count }}</td>
                            <td>{{ stat.conversion_cost }}</td>
                            <td>{{ stat.visit_count }}</td>
                            <td>{{ stat.visit_cost }}</td>
                            <td>{{ stat.total_amount }}</td>
                            <td>{{ stat.roi }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="chart-container">
            <div id="ownerChart" class="bottom-chart"></div>
        </div>

        <div class="charts-row">
            <div class="chart-container">
                <div id="hourlyValidChart" class="chart"></div>
            </div>
            <div class="chart-container">
                <div id="hourlyRateChart" class="chart"></div>
            </div>
        </div>
    </div>

    <div class="float-nav">
        <a href="/ks" class="float-btn" title="快手数据分析">
            <i class="nav-icon">快手</i>
        </a>
        <a href="/" class="float-btn" title="返回首页">
            <i class="nav-icon">首页</i>
        </a>
    </div>

    <script>
        // 在原有的图表初始化代码前添加
        function initCharts() {
            if (typeof echarts === 'undefined') {
                console.log('等待 ECharts 加载...');
                setTimeout(initCharts, 100);
                return;
            }

            // 添加账户筛选功能
            const accountSelect = document.getElementById('account-select');
            const adStatsTable = document.querySelector('#ad-stats .data-table tbody');
            const originalRows = Array.from(adStatsTable.querySelectorAll('tr'));

            accountSelect.addEventListener('change', function() {
                const selectedAccount = this.value;
                
                // 清空表格
                adStatsTable.innerHTML = '';
                
                // 筛选并添加符合条件的行
                originalRows.forEach(row => {
                    const accountCell = row.querySelector('td:nth-child(2)'); // 获取账户列
                    if (!selectedAccount || accountCell.textContent === selectedAccount) {
                        adStatsTable.appendChild(row.cloneNode(true));
                    }
                });
            });

            // 原有的图表初始化代码
            const colorPalette = ['#1890ff', '#36cfc9', '#73d13d', '#ffc53d', '#ff7a45', '#ff4d4f'];
            
            // 初始化图表
            const labelChart = echarts.init(document.getElementById('labelChart'));
            const stageChart = echarts.init(document.getElementById('stageChart'));
            const ownerChart = echarts.init(document.getElementById('ownerChart'));
            
            // 格式化百分比的函数
            function formatPercentage(params) {
                const total = params.data.series.reduce((sum, item) => sum + item.value, 0);
                const percentage = ((params.value / total) * 100).toFixed(1);
                return `${params.name}: ${params.value} (${percentage}%)`;
            }

            // 线索标签饼图
            const labelOptions = {
                color: colorPalette,
                title: {
                    text: '线索标签分布',
                    left: 'center',
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        const total = labelChart.getOption().series[0].data.reduce((sum, item) => sum + item.value, 0);
                        const percentage = ((params.value / total) * 100).toFixed(1);
                        return `${params.name}<br/>${params.value} (${percentage}%)`;
                    }
                },

                series: [{
                    name: '线索标签',
                    type: 'pie',
                    radius: '60%',
                    data: {{ label_series | tojson | safe }},
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        show: true,
                        formatter: '{b}: {d}%'
                    }
                }]
            };
            labelChart.setOption(labelOptions);

            // 线索阶段饼图
            const stageOptions = {
                color: colorPalette,
                title: {
                    text: '线索阶段分布',
                    left: 'center',
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        const total = stageChart.getOption().series[0].data.reduce((sum, item) => sum + item.value, 0);
                        const percentage = ((params.value / total) * 100).toFixed(1);
                        return `${params.name}<br/>${params.value} (${percentage}%)`;
                    }
                },
                series: [{
                    name: '线索阶段',
                    type: 'pie',
                    radius: '60%',
                    data: {{ stage_series | tojson | safe }},
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        show: true,
                        formatter: '{b}: {d}%'
                    }
                }]
            };
            stageChart.setOption(stageOptions);

            // 所属人柱状图
            const ownerOptions = {
                color: ['#1890ff'],
                title: {
                    text: '所属人电话数量统计',
                    left: 'center',
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'axis'
                },
                xAxis: {
                    type: 'category',
                    data: {{ owner_names | tojson | safe }},
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value'
                },
                series: [{
                    data: {{ owner_values | tojson | safe }},
                    type: 'bar',
                    barWidth: '60%',
                    itemStyle: {
                        borderRadius: [5, 5, 0, 0]
                    }
                }]
            };
            ownerChart.setOption(ownerOptions);

            const noteChart = echarts.init(document.getElementById('noteChart'));
            
            // 备注分布饼图
            const noteOptions = {
                color: colorPalette,
                title: {
                    text: '备注分布',
                    left: 'center',
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        const total = noteChart.getOption().series[0].data.reduce((sum, item) => sum + item.value, 0);
                        const percentage = ((params.value / total) * 100).toFixed(1);
                        return `${params.name}<br/>${params.value} (${percentage}%)`;
                    }
                },
                series: [{
                    name: '备注',
                    type: 'pie',
                    radius: '60%',
                    data: {{ note_series | tojson | safe }},
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    label: {
                        show: true,
                        formatter: '{b}: {d}%'
                    }
                }]
            };
            noteChart.setOption(noteOptions);

            // 初始化小时总线索数量折线图
            const hourlyValidChart = echarts.init(document.getElementById('hourlyValidChart'));
            const hourlyValidOption = {
                title: {
                    text: '各时段总线索数量',
                    left: 'center',
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return `${params[0].name}时<br/>${params[0].seriesName}：${params[0].value}个`;
                    }
                },
                xAxis: {
                    type: 'category',
                    data: {{ hourly_data.hours|tojson|safe }},
                    name: '时间（小时）',
                    axisLabel: {
                        formatter: '{value}:00'
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '总线索数量'
                },
                series: [{
                    name: '总线索数量',
                    type: 'line',
                    data: {{ hourly_data.total_counts|tojson|safe }},
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 8,
                    itemStyle: {
                        color: '#1890ff'
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: 'rgba(24,144,255,0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(24,144,255,0.1)'
                        }])
                    },
                    label: {
                        show: true,
                        position: 'top',
                        formatter: '{c}'
                    }
                }]
            };
            hourlyValidChart.setOption(hourlyValidOption);

            // 初始化小时有效率折线图
            const hourlyRateChart = echarts.init(document.getElementById('hourlyRateChart'));
            const hourlyRateOption = {
                title: {
                    text: '各时段线索有效率',
                    left: 'center',
                    textStyle: {
                        color: '#333'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return `${params[0].name}时<br/>${params[0].seriesName}：${params[0].value.toFixed(2)}%`;
                    }
                },
                xAxis: {
                    type: 'category',
                    data: {{ hourly_data.hours|tojson|safe }},
                    name: '时间（小时）',
                    axisLabel: {
                        formatter: '{value}:00'
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '有效率（%）',
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [{
                    name: '有效率',
                    type: 'line',
                    data: {{ hourly_data.valid_rates|tojson|safe }},
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 8,
                    itemStyle: {
                        color: '#73d13d'
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                            offset: 0,
                            color: 'rgba(115,209,61,0.3)'
                        }, {
                            offset: 1,
                            color: 'rgba(115,209,61,0.1)'
                        }])
                    },
                    label: {
                        show: true,
                        position: 'top',
                        formatter: '{c}%'
                    }
                }]
            };
            hourlyRateChart.setOption(hourlyRateOption);

            // 响应式调整
            window.addEventListener('resize', function() {
                labelChart.resize();
                stageChart.resize();
                ownerChart.resize();
                noteChart.resize();
                hourlyValidChart.resize();
                hourlyRateChart.resize();
            });

            document.getElementById('dateFilterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const startDate = document.getElementById('start_date').value;
                const endDate = document.getElementById('end_date').value;
                
                // 验证日期
                if (startDate > endDate) {
                    alert('开始时间不能大于结束时间');
                    return;
                }
                
                // 构建URL并跳转
                const url = new URL(window.location.href);
                url.searchParams.set('start_date', startDate);
                url.searchParams.set('end_date', endDate);
                window.location.href = url.toString();
            });

            // 确保结束时间不能小于开始时间
            document.getElementById('start_date').addEventListener('change', function() {
                document.getElementById('end_date').min = this.value;
            });

            // 确保开始时间不能大于结束时间
            document.getElementById('end_date').addEventListener('change', function() {
                document.getElementById('start_date').max = this.value;
            });
        }

        // 页面加载完成后开始初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 获取当前页面路径
            const currentPath = window.location.pathname;
            
            // 获取所有导航按钮
            const navButtons = document.querySelectorAll('.nav-btn');
            
            // 根据当前路径设置活动按钮
            navButtons.forEach(btn => {
                if (btn.getAttribute('href') === currentPath) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
            
            // 其他初始化代码...
            initCharts();

            document.querySelectorAll('.tab-btn').forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有按钮的 active 类
                    document.querySelectorAll('.tab-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    
                    // 移除所有内容的 active 类
                    document.querySelectorAll('.tab-content').forEach(content => {
                        content.classList.remove('active');
                    });
                    
                    // 添加当前按钮的 active 类
                    this.classList.add('active');
                    
                    // 显示对应的内容
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        });
    </script>
</body>
</html>