# 现场咨询师来院数据统计大屏系统

## 🎯 系统概述

这是一个专为大屏显示设计的现场咨询师来院数据统计可视化系统，采用深色主题和全屏布局，适合会议室、监控中心等场景使用。

## 🚀 新增功能

### ✅ 已修复的问题
- **JSON序列化错误**：修复了pandas数据类型导致的序列化问题
- **数据类型转换**：所有数值都转换为Python原生类型

### 🎨 大屏设计特性
- **全屏显示**：100vh高度，无滚动条设计
- **深色主题**：适合长时间观看，减少眼部疲劳
- **渐变背景**：科技感十足的深蓝色渐变背景
- **发光效果**：图表和文字带有发光阴影效果
- **实时时间**：右上角显示当前日期时间

### 📊 时间间隔功能
- **可选间隔**：支持7天、14天、30天时间间隔
- **智能计算**：从昨天开始往前推算指定天数
- **趋势分析**：X轴显示时间段，Y轴显示对应数据

## 🌐 访问地址

- **普通页面**：http://localhost:5001
- **大屏页面**：http://localhost:5001/dashboard

## 📱 页面布局

### 顶部区域 (80px)
```
┌─────────────────────────────────────────────────────────────┐
│  现场咨询师来院数据统计大屏        2025-08-18 11:45:30      │
└─────────────────────────────────────────────────────────────┘
```

### 控制面板 (120px)
```
┌─────────────────────────────────────────────────────────────┐
│ 开始日期: [2025-08-04] 结束日期: [2025-08-18]              │
│ 时间间隔: [14天▼] 咨询师: [请选择咨询师▼] [查询数据]        │
└─────────────────────────────────────────────────────────────┘
```

### 汇总卡片区域 (200px)
```
┌─────────┬─────────┬─────────┬─────────┬─────────┐
│总开单业绩│  总人数  │ 成交人数 │ 成交率(%)│  客单价  │
│1,500,000│   150   │   45    │  30.0   │ 33,333  │
└─────────┴─────────┴─────────┴─────────┴─────────┘
```

### 主图表区域 (剩余空间)
```
┌─────────────────────┬─────────────────────┐
│    客户类型占比      │   客户类型分布       │
│     (饼状图)        │   (堆叠柱状图)       │
├─────────────────────┼─────────────────────┤
│   咨询师业绩对比     │    成交率对比        │
│    (柱状图)         │    (柱状图)         │
└─────────────────────┴─────────────────────┘
```

### 趋势图表区域 (300px)
```
┌─────────┬─────────┬─────────┬─────────┐
│总人数趋势│客户类型趋势│成交率趋势│客单价趋势│
│(折线图) │ (折线图) │ (折线图) │ (折线图) │
└─────────┴─────────┴─────────┴─────────┘
```

## 🎨 视觉设计

### 配色方案
- **主色调**：#00d4ff (青蓝色)
- **辅助色**：#00ff88 (绿色)
- **强调色**：#ff6b6b (红色)
- **背景色**：深蓝渐变 (#0c1e3f → #1a365d → #2d3748)
- **文字色**：#e2e8f0 (浅灰)

### 特效设计
- **发光效果**：文字和图表带有彩色阴影
- **渐变填充**：柱状图和折线图使用渐变色
- **毛玻璃效果**：卡片背景使用backdrop-filter
- **悬停动画**：按钮和图表支持交互动画

## 📊 图表配置

### 饼状图 - 客户类型占比
- **类型**：环形饼图
- **数据**：老客、新客首次、新客二次
- **特效**：发光阴影、悬停放大

### 柱状图 - 多维度对比
1. **客户类型分布**：堆叠柱状图，渐变填充
2. **咨询师业绩对比**：单系列柱状图，红色渐变
3. **成交率对比**：单系列柱状图，绿色渐变

### 折线图 - 趋势分析
1. **总人数趋势**：面积填充，青蓝色
2. **客户类型趋势**：多系列折线，三色区分
3. **成交率趋势**：面积填充，绿色
4. **客单价趋势**：面积填充，红色

## ⚙️ 时间间隔功能详解

### 工作原理
1. **基准时间**：从昨天开始计算
2. **间隔设置**：用户选择7天、14天或30天
3. **数据点生成**：每个间隔生成一个数据点
4. **时间轴显示**：X轴显示各个时间段的日期

### 示例说明
假设今天是2025-08-18，选择14天间隔：
- 数据点1：2025-08-17 (昨天)
- 数据点2：2025-08-03 (往前14天)
- 数据点3：2025-07-20 (再往前14天)
- ...以此类推

## 🔧 技术实现

### 后端修复
```python
# 修复JSON序列化问题
total_performance = float(df['总开单业绩'].sum())
total_people = int(df['总人数'].sum())
# 确保所有数值都是Python原生类型
```

### 前端优化
```javascript
// 深色主题配置
const darkTheme = {
    backgroundColor: 'transparent',
    textStyle: { color: '#e2e8f0' }
};

// 时间间隔计算
const intervalDays = parseInt(document.getElementById('interval-days').value);
```

### CSS特效
```css
/* 发光效果 */
text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);

/* 渐变背景 */
background: linear-gradient(135deg, #0c1e3f 0%, #1a365d 50%, #2d3748 100%);

/* 毛玻璃效果 */
backdrop-filter: blur(10px);
```

## 🚀 使用方法

### 1. 启动应用
```bash
python consultant_stats_app.py
```

### 2. 访问大屏页面
打开浏览器访问：http://localhost:5001/dashboard

### 3. 设置参数
- 选择开始和结束日期
- 设置时间间隔（7天/14天/30天）
- 可选择特定咨询师查看个人趋势

### 4. 查看数据
- 点击"查询数据"按钮获取最新统计
- 图表会自动更新显示
- 趋势图根据时间间隔显示数据点

## 📱 响应式支持

### 大屏显示 (>1600px)
- 5列汇总卡片
- 2x2主图表布局
- 4列趋势图表

### 中等屏幕 (1200px-1600px)
- 字体大小自动调整
- 图表尺寸优化

### 小屏幕 (<1200px)
- 单列主图表布局
- 2x2趋势图表布局

## 🎯 适用场景

- **会议室大屏**：实时展示业务数据
- **监控中心**：24小时数据监控
- **管理驾驶舱**：高层决策支持
- **营销展示**：客户演示和汇报

## 🔄 自动刷新

可以通过JavaScript添加自动刷新功能：
```javascript
// 每5分钟自动刷新数据
setInterval(loadData, 5 * 60 * 1000);
```

## 🎉 总结

现在你拥有了一个功能完整的大屏数据可视化系统：

✅ **修复了JSON序列化错误**
✅ **全屏大屏设计**
✅ **深色科技主题**
✅ **时间间隔功能**
✅ **实时时间显示**
✅ **响应式布局**
✅ **丰富的图表展示**
✅ **发光特效设计**

访问 http://localhost:5001/dashboard 即可体验完整的大屏数据可视化效果！🎉
