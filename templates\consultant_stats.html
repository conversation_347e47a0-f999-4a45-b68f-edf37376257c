<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现场咨询师来院数据统计</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .title {
            font-size: 32px;
            font-weight: bold;
            color: #1890ff;
            margin-bottom: 20px;
        }

        .date-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .date-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .date-group label {
            font-weight: bold;
            color: #666;
        }

        .date-group input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .query-btn {
            padding: 10px 20px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
        }

        .query-btn:hover {
            background: #40a9ff;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .summary-card .label {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
        }

        .summary-card .value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }

        .chart-container {
            background: white;
            margin-bottom: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .chart-title {
            padding: 20px;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
        }

        .chart {
            width: 100%;
            height: 400px;
        }

        .chart.large {
            height: 500px;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .consultant-selector {
            margin: 20px;
            text-align: center;
        }

        .consultant-selector select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            margin-left: 10px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #ff4d4f;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 标题 -->
        <div class="header">
            <h1 class="title">现场咨询师来院数据统计</h1>
        </div>

        <!-- 日期选择 -->
        <div class="date-controls">
            <div class="date-group">
                <label for="start-date">开始日期:</label>
                <input type="date" id="start-date" value="{{ start_date }}">
            </div>
            <div class="date-group">
                <label for="end-date">结束日期:</label>
                <input type="date" id="end-date" value="{{ end_date }}">
            </div>
            <button class="query-btn" onclick="loadData()">查询</button>
        </div>

        <!-- 汇总数据卡片 -->
        <div class="summary-cards" id="summary-cards">
            <div class="summary-card">
                <div class="label">总开单业绩</div>
                <div class="value" id="total-performance">-</div>
            </div>
            <div class="summary-card">
                <div class="label">总人数</div>
                <div class="value" id="total-people">-</div>
            </div>
            <div class="summary-card">
                <div class="label">成交人数</div>
                <div class="value" id="total-deal">-</div>
            </div>
            <div class="summary-card">
                <div class="label">成交率(%)</div>
                <div class="value" id="deal-rate">-</div>
            </div>
            <div class="summary-card">
                <div class="label">客单价</div>
                <div class="value" id="avg-price">-</div>
            </div>
        </div>

        <!-- 饼状图 - 客户类型占比 -->
        <div class="chart-container">
            <div class="chart-title">客户类型占比</div>
            <div id="pie-chart" class="chart"></div>
        </div>

        <!-- 柱状图网格 -->
        <div class="chart-grid">
            <!-- 客户类型堆叠柱状图 -->
            <div class="chart-container">
                <div class="chart-title">各咨询师客户类型分布</div>
                <div id="customer-type-bar" class="chart"></div>
            </div>

            <!-- 总开单业绩柱状图 -->
            <div class="chart-container">
                <div class="chart-title">各咨询师总开单业绩</div>
                <div id="performance-bar" class="chart"></div>
            </div>

            <!-- 成交率柱状图 -->
            <div class="chart-container">
                <div class="chart-title">各咨询师成交率</div>
                <div id="deal-rate-bar" class="chart"></div>
            </div>

            <!-- 客单价柱状图 -->
            <div class="chart-container">
                <div class="chart-title">各咨询师客单价</div>
                <div id="avg-price-bar" class="chart"></div>
            </div>
        </div>

        <!-- 咨询师选择器 -->
        <div class="consultant-selector">
            <label for="consultant-select">选择咨询师查看趋势:</label>
            <select id="consultant-select" onchange="loadTrendData()">
                <option value="">请选择咨询师</option>
            </select>
        </div>

        <!-- 折线图网格 -->
        <div class="chart-grid">
            <!-- 总人数趋势 -->
            <div class="chart-container">
                <div class="chart-title">总人数趋势</div>
                <div id="people-trend" class="chart"></div>
            </div>

            <!-- 客户类型趋势 -->
            <div class="chart-container">
                <div class="chart-title">客户类型趋势</div>
                <div id="customer-trend" class="chart"></div>
            </div>

            <!-- 成交率趋势 -->
            <div class="chart-container">
                <div class="chart-title">成交率趋势</div>
                <div id="deal-rate-trend" class="chart"></div>
            </div>

            <!-- 客单价趋势 -->
            <div class="chart-container">
                <div class="chart-title">客单价趋势</div>
                <div id="avg-price-trend" class="chart"></div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='consultant_stats.js') }}"></script>
</body>
</html>
