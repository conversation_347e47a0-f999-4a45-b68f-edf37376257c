#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试巨量引擎视频API功能
"""

import requests
import json


def test_video_info_api():
    """
    测试视频信息获取API
    """
    print("=== 测试巨量引擎视频信息API ===\n")
    
    # API配置
    url = "https://business.oceanengine.com/nbs/api/bm/asset/video/get_video_info"
    
    # 请求头
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'origin': 'https://business.oceanengine.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://business.oceanengine.com/site/asset/material_center/management/video',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
        'x-csrf-token': '4602caf96aa321248fbba9fdbba931ca11755595411',
        'x-csrftoken': 'YxswAO5XaPtoAFNBSItaJB0Q'
    }
    
    # Cookie字符串转换为字典
    cookie_string = "ttcid=a4037d82677d42d782bddc5a2abfe70217; gr_user_id=d4c9e21c-8ecc-4f21-8a05-9d74c5d2348e; grwng_uid=f29c2a4f-**************-9cdb6bb0b35e; store-region=cn-ah; store-region-src=uid; is_hit_partitioned_cookie_canary_ss=true; is_hit_partitioned_cookie_canary_ss=true; is_staff_user=false; is_hit_partitioned_cookie_canary=true; is_hit_partitioned_cookie_canary=true; n_mh=we4sdJUEQEJR2yFJVWSKhZ2TUDg4P82QmFtCQsF_ze0; tt_scid=EsuDluvolF.lEn32DlEfDDQ7AGMARwRoAKZUfXZzxwYxlAUQqo7Ba.F8IVwHZOGO98b1; passport_csrf_token=cdf96cd64b0960a8c55788e19295e12f; passport_csrf_token_default=cdf96cd64b0960a8c55788e19295e12f; passport_mfa_token=CjeeDV7gHtYeXEq50uxjUNE%2FZCXYmuGkL3T6Xurov4sHkbhurORjG5PXBvpqZo6gz8poK64kRO%2FvGkoKPAAAAAAAAAAAAABPMQE55VQEULQtuKzM2M1EshQBaL8%2FJ2pmgXQg5CaXfGT2O8EIA8crJkb6qb2OjTmhshCz6PUNGPax0WwgAiIBA9LvtbY%3D; d_ticket=9ed830468ab37cea9d04fd0626a7057430095; sso_uid_tt=2ce26173525f16c27850a8a16cb70125; sso_uid_tt_ss=2ce26173525f16c27850a8a16cb70125; toutiao_sso_user=1b94430137b7c38c79bd5ac1b2a785a7; toutiao_sso_user_ss=1b94430137b7c38c79bd5ac1b2a785a7; sid_ucp_sso_v1=1.0.0-KGYyMzE5YjYwMGUyNmMyZjNlZjIwOTU0ZGQ3MTc3MzcxM2IyZjdmMjYKHwizi_Cugs2dBRDo-pzDBhj6CiAMMJO8ibQGOAJA8QcaAmxmIiAxYjk0NDMwMTM3YjdjMzhjNzliZDVhYzFiMmE3ODVhNw; ssid_ucp_sso_v1=1.0.0-KGYyMzE5YjYwMGUyNmMyZjNlZjIwOTU0ZGQ3MTc3MzcxM2IyZjdmMjYKHwizi_Cugs2dBRDo-pzDBhj6CiAMMJO8ibQGOAJA8QcaAmxmIiAxYjk0NDMwMTM3YjdjMzhjNzliZDVhYzFiMmE3ODVhNw; uid_tt=d9e661fca3791b4e4749c271282c6b8d; uid_tt_ss=d9e661fca3791b4e4749c271282c6b8d; uid_tt_ss=d9e661fca3791b4e4749c271282c6b8d; sid_tt=1b9a22c0421bd13ec0b5944520a14432; sessionid=1b9a22c0421bd13ec0b5944520a14432; sessionid_ss=1b9a22c0421bd13ec0b5944520a14432; sessionid_ss=1b9a22c0421bd13ec0b5944520a14432; sid_ucp_v1=1.0.0-KDMwZjhkNjEyZmQzODFmMzRhZWQ1NjQ4ODk1NjBiYWY1OTFhN2E2ZmIKGQizi_Cugs2dBRDo-pzDBhj6CiAMOAJA8QcaAmxmIiAxYjlhMjJjMDQyMWJkMTNlYzBiNTk0NDUyMGExNDQzMg; ssid_ucp_v1=1.0.0-KDMwZjhkNjEyZmQzODFmMzRhZWQ1NjQ4ODk1NjBiYWY1OTFhN2E2ZmIKGQizi_Cugs2dBRDo-pzDBhj6CiAMOAJA8QcaAmxmIiAxYjlhMjJjMDQyMWJkMTNlYzBiNTk0NDUyMGExNDQzMg; odin_tt=3af133251b4066689b37ff801c871259543835622173ccf3d9be54585e815a475e2b1e708778e68f31c2a8f66b1942218166aefe8c588846f88655852f78ed38; sid_guard=1b9a22c0421bd13ec0b5944520a14432%7C1751934458%7C5184000%7CSat%2C+06-Sep-2025+00%3A27%3A38+GMT; sid_ucp_v1=1.0.0-KDE1NjE1NmUzOTQ3M2Q3YjgzOTUwMmRlY2U5NjdkZWU5ODI4YWM1MzYKGQizi_Cugs2dBRD6y7HDBhj6CiAMOAJA8QcaAmxmIiAxYjlhMjJjMDQyMWJkMTNlYzBiNTk0NDUyMGExNDQzMg; ssid_ucp_v1=1.0.0-KDE1NjE1NmUzOTQ3M2Q3YjgzOTUwMmRlY2U5NjdkZWU5ODI4YWM1MzYKGQizi_Cugs2dBRD6y7HDBhj6CiAMOAJA8QcaAmxmIiAxYjlhMjJjMDQyMWJkMTNlYzBiNTk0NDUyMGExNDQzMg; a7b76a7621d895df_gr_last_sent_cs1=1827622897523092; a7b76a7621d895df_gr_cs1=1827622897523092; session_tlb_tag=sttt%7C8%7CG5oiwEIb0T7AtZRFIKFEMv_________LJ1996br1u0wh8D4H829RZ0I7yWiLm2LcSqCxEu6MbgE%3D; session_tlb_tag=sttt%7C8%7CG5oiwEIb0T7AtZRFIKFEMv_________LJ1996br1u0wh8D4H829RZ0I7yWiLm2LcSqCxEu6MbgE%3D; aefa4e5d2593305f_gr_last_sent_cs1=1806720071681024; aefa4e5d2593305f_gr_cs1=1806720071681024; ttwid=1%7ChJTaYfvaIDaDkop7Tptyv1EV2Cab28wCNVAvrOxu8gs%7C1754292970%7Cbb18cb7fea1bbb80028faeb834620b98ba9add5c602075f2a5163c192fcafe31; s_v_web_id=verify_mdz8mag8_Ern5C7ZB_4xPK_4Q0C_98h0_MqGUOAmRVn6o; csrftoken=YxswAO5XaPtoAFNBSItaJB0Q; csrf_session_id=01516454031d4b94ed64d3b35c6cfc57; trace_log_adv_id=; trace_log_user_id=2944939450959283"
    
    # 解析Cookie
    cookies = {}
    for item in cookie_string.split('; '):
        if '=' in item:
            key, value = item.split('=', 1)
            cookies[key] = value
    
    # 请求数据
    data = {
        "vids": ["v02033g10000d22ps7vog65rh4ndrugg"]
    }
    
    print(f"🔍 正在请求视频信息...")
    print(f"   URL: {url}")
    print(f"   视频ID: {data['vids'][0]}")
    
    try:
        response = requests.post(url, headers=headers, cookies=cookies, json=data, timeout=10)
        
        print(f"📡 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('code') == 0:
                print("✅ API调用成功！")
                
                video_data = result.get('data', {}).get('data', {})
                video_id = data['vids'][0]
                
                if video_id in video_data:
                    info = video_data[video_id]
                    
                    print(f"\n📹 视频信息:")
                    print(f"   🆔 视频ID: {info.get('video_id')}")
                    print(f"   🔗 视频URL: {info.get('video_url')}")
                    print(f"   🎬 原始视频URL: {info.get('original_video_url')}")
                    print(f"   🖼️  封面URL: {info.get('cover_url')}")
                    print(f"   📏 视频尺寸: {info.get('vwidth')}x{info.get('vheight')}")
                    print(f"   ⏱️  视频时长: {info.get('video_duration')} 秒")
                    print(f"   💾 文件大小: {info.get('video_size')} bytes ({info.get('video_size', 0) / 1024 / 1024:.2f} MB)")
                    print(f"   🎵 比特率: {info.get('bitrate')}")
                    print(f"   📄 格式: {info.get('format')}")
                    print(f"   🔐 文件哈希: {info.get('file_hash')}")
                    print(f"   📊 状态: {info.get('status')}")
                    
                    # 重点：提取video_url
                    video_url = info.get('video_url')
                    if video_url:
                        print(f"\n🎯 提取的video_url:")
                        print(f"   {video_url}")
                        
                        # 验证URL是否可访问
                        print(f"\n🔍 验证视频URL可访问性...")
                        try:
                            head_response = requests.head(video_url, timeout=5)
                            print(f"   状态码: {head_response.status_code}")
                            if head_response.status_code == 200:
                                print("   ✅ 视频URL可访问")
                                content_length = head_response.headers.get('content-length')
                                if content_length:
                                    print(f"   📏 内容长度: {content_length} bytes")
                            else:
                                print("   ⚠️  视频URL可能需要特殊认证")
                        except Exception as e:
                            print(f"   ❌ URL验证失败: {e}")
                    
                    return info
                else:
                    print(f"❌ 响应中未找到视频ID: {video_id}")
            else:
                print(f"❌ API返回错误: {result.get('msg', '未知错误')}")
                print(f"   错误代码: {result.get('code')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")
    
    return None


if __name__ == "__main__":
    test_video_info_api()
