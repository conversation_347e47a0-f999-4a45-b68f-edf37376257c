import requests
import json
from datetime import datetime, timed<PERSON><PERSON>


def get_cookies_from_browser():
    """
    获取浏览器Cookie的说明函数

    Returns:
        str: 获取Cookie的说明
    """
    instructions = """
    获取Cookie和Token的步骤：

    1. 打开Chrome浏览器，登录到 https://business.oceanengine.com
    2. 进入素材中心 -> 视频管理页面
    3. 按F12打开开发者工具
    4. 切换到Network标签页
    5. 刷新页面或执行一个视频列表请求
    6. 找到对应的API请求，右键 -> Copy -> Copy as cURL
    7. 从cURL中提取以下信息：
       - Cookie字符串（完整的Cookie头）
       - x-csrftoken值
       - x-csrf-token值

    示例Cookie格式：
    {
        'sessionid': 'xxx',
        'csrftoken': 'xxx',
        'uid': 'xxx',
        'sid': 'xxx',
        # ... 其他cookie
    }
    """
    return instructions


def parse_cookie_string(cookie_string):
    """
    解析Cookie字符串为字典

    Args:
        cookie_string (str): 从浏览器复制的Cookie字符串

    Returns:
        dict: Cookie字典
    """
    cookies = {}
    if cookie_string:
        for item in cookie_string.split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                cookies[key] = value
    return cookies


def extract_csrf_from_cookies(cookies):
    """
    从Cookie中提取CSRF token

    Args:
        cookies (dict): Cookie字典

    Returns:
        str: CSRF token
    """
    # 尝试从不同的cookie字段中获取csrf token
    csrf_fields = ['csrftoken', 'csrf_token', '_csrf_token', 'csrf']

    for field in csrf_fields:
        if field in cookies:
            return cookies[field]

    return None


def validate_tokens(csrf_token, x_csrf_token, cookies):
    """
    验证token的一致性

    Args:
        csrf_token (str): x-csrftoken头的值
        x_csrf_token (str): x-csrf-token头的值
        cookies (dict): Cookie字典

    Returns:
        tuple: (是否有效, 建议的csrf_token)
    """
    cookie_csrf = extract_csrf_from_cookies(cookies)

    print(f"Cookie中的CSRF token: {cookie_csrf}")
    print(f"x-csrftoken头: {csrf_token}")
    print(f"x-csrf-token头: {x_csrf_token}")

    # 检查一致性
    if cookie_csrf and csrf_token:
        if cookie_csrf == csrf_token:
            return True, csrf_token
        else:
            print("警告: Cookie中的csrftoken与x-csrftoken头不匹配")
            return False, cookie_csrf

    if cookie_csrf:
        print("建议使用Cookie中的csrftoken")
        return False, cookie_csrf

    return False, csrf_token


def get_csrf_from_page():
    """
    获取CSRF token的详细说明
    """
    instructions = """
    获取正确CSRF token的步骤：

    方法1 - 从Network请求中获取：
    1. 打开Chrome开发者工具 (F12)
    2. 切换到Network标签
    3. 刷新页面或执行视频列表操作
    4. 找到 /nbs/api/bm/video/list 请求
    5. 查看Request Headers中的：
       - x-csrftoken: xxxxx
       - x-csrf-token: xxxxx
       - Cookie: csrftoken=xxxxx; ...

    方法2 - 从页面源码中获取：
    1. 在页面上右键 -> 查看页面源代码
    2. 搜索 "csrftoken" 或 "csrf"
    3. 找到类似这样的内容：
       <meta name="csrf-token" content="xxxxx">
       或
       window.csrfToken = "xxxxx"

    方法3 - 从Console中获取：
    1. 打开Console标签
    2. 输入: document.cookie
    3. 查找csrftoken的值
    4. 或输入: document.querySelector('meta[name="csrf-token"]').content

    重要提示：
    - x-csrftoken 和 Cookie中的csrftoken 必须一致
    - token通常在登录后生成，确保已登录
    - token可能有时效性，需要及时更新
    """
    return instructions


def parse_curl_command(curl_command):
    """
    从cURL命令中解析出所需的参数

    Args:
        curl_command (str): 从浏览器复制的cURL命令

    Returns:
        dict: 包含解析出的参数
    """
    import re

    result = {
        'csrf_token': None,
        'x_csrf_token': None,
        'cookies': {},
        'group_id': None
    }

    # 提取x-csrftoken
    csrf_match = re.search(r'-H ["\']x-csrftoken:\s*([^"\']+)["\']', curl_command)
    if csrf_match:
        result['csrf_token'] = csrf_match.group(1).strip()

    # 提取x-csrf-token
    x_csrf_match = re.search(r'-H ["\']x-csrf-token:\s*([^"\']+)["\']', curl_command)
    if x_csrf_match:
        result['x_csrf_token'] = x_csrf_match.group(1).strip()

    # 提取Cookie
    cookie_match = re.search(r'-H ["\']Cookie:\s*([^"\']+)["\']', curl_command)
    if cookie_match:
        cookie_string = cookie_match.group(1).strip()
        result['cookies'] = parse_cookie_string(cookie_string)

    # 提取group_id
    group_match = re.search(r'group_id[=:]([^&\s"\']+)', curl_command)
    if group_match:
        result['group_id'] = group_match.group(1).strip()

    return result


def get_video_url(group_id, material_id, csrf_token, x_csrf_token, cookies=None, start_date=None, end_date=None):
    """
    获取视频URL

    Args:
        group_id (str): 组ID
        material_id (str): 素材ID
        csrf_token (str): CSRF token
        x_csrf_token (str): X-CSRF token
        cookies (dict, optional): 浏览器Cookie字典
        start_date (str, optional): 开始日期，格式：YYYY-MM-DD，默认为7天前
        end_date (str, optional): 结束日期，格式：YYYY-MM-DD，默认为今天

    Returns:
        dict: 包含视频信息的字典，如果失败返回None
    """

    # 设置默认日期范围（最近7天）
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    if not start_date:
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

    # 验证和修正CSRF token
    if cookies:
        is_valid, suggested_csrf = validate_tokens(csrf_token, x_csrf_token, cookies)
        if not is_valid:
            csrf_token = suggested_csrf

    # API URL
    url = f"https://business.oceanengine.com/nbs/api/bm/video/list?group_id={group_id}"

    # 请求头
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        "Content-Type": "application/json",
        "Origin": "https://business.oceanengine.com",
        "Pragma": "no-cache",
        "Referer": f"https://business.oceanengine.com/site/asset/material_center/management/video?cc_id={group_id}",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-csrftoken": csrf_token,
        "x-csrf-token": x_csrf_token,
    }

    # 请求数据
    data = {
        "group_id": group_id,
        "version": "v2",
        "material_platform": 49,
        "tags": [],
        "image_mode_filter": [],
        "material_property_filter": [],
        "sources": [],
        "order_by": "create_time",
        "is_asc": False,
        "file_name": material_id if material_id else "",
        "include_metrics": ["convert_cnt", "conversion_cost", "conversion_rate"],
        "metrics_start_time": f"{start_date} 00:00:00",
        "metrics_end_time": f"{end_date} 00:00:00",
        "page": 1,
        "limit": 20,
        "owner": {
            "owner_id": group_id,
            "owner_type": 9
        }
    }

    try:
        # 创建session以保持连接
        session = requests.Session()

        # 如果提供了cookies，添加到session中
        if cookies:
            session.cookies.update(cookies)

        # 发送POST请求
        response = session.post(url, headers=headers, json=data, timeout=30)

        response.raise_for_status()

        # 解析响应
        try:
            result = response.json()
        except json.JSONDecodeError as e:
            print(f"JSON解析失败，响应内容前200字符: {response.text[:200]}...")
            print(f"响应编码: {response.encoding}")
            print(f"Content-Type: {response.headers.get('Content-Type')}")
            raise e
        


        if result.get("code") == 0 and result.get("data", {}).get("videos"):
            videos = result["data"]["videos"]
            if videos:
                video = videos[0]  # 取第一个视频
                return {
                    "video_id": video.get("video_id"),
                    "video_url": video.get("video_url"),
                    "video_name": video.get("video_name"),
                    "video_poster": video.get("video_poster"),
                    "material_id": video.get("material_id"),
                    "create_time": video.get("create_time"),
                    "duration": video.get("video_info", {}).get("duration"),
                    "width": video.get("video_info", {}).get("width"),
                    "height": video.get("video_info", {}).get("height"),
                    "metrics": video.get("metrics", {})
                }

        print(f"API返回错误: {result}")
        return None

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return None
    except Exception as e:
        print(f"未知错误: {e}")
        return None


def get_multiple_videos(group_id, csrf_token, x_csrf_token, cookies=None, page=1, limit=20, start_date=None, end_date=None):
    """
    获取多个视频信息

    Args:
        group_id (str): 组ID
        csrf_token (str): CSRF token
        x_csrf_token (str): X-CSRF token
        page (int): 页码，默认为1
        limit (int): 每页数量，默认为20
        start_date (str, optional): 开始日期，格式：YYYY-MM-DD
        end_date (str, optional): 结束日期，格式：YYYY-MM-DD

    Returns:
        dict: 包含视频列表和分页信息的字典
    """

    # 设置默认日期范围（最近7天）
    if not end_date:
        end_date = datetime.now().strftime('%Y-%m-%d')
    if not start_date:
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')

    # API URL
    url = f"https://business.oceanengine.com/nbs/api/bm/video/list?group_id={group_id}"

    # 请求头
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
        "Cache-Control": "no-cache",
        "Content-Type": "application/json",
        "Origin": "https://business.oceanengine.com",
        "Pragma": "no-cache",
        "Referer": f"https://business.oceanengine.com/site/asset/material_center/management/video?cc_id={group_id}",
        "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "Sec-Fetch-Dest": "empty",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Site": "same-origin",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-csrftoken": csrf_token,
        "x-csrf-token": x_csrf_token,
    }

    # 请求数据
    data = {
        "group_id": group_id,
        "version": "v2",
        "material_platform": 49,
        "tags": [],
        "image_mode_filter": [],
        "material_property_filter": [],
        "sources": [],
        "order_by": "create_time",
        "is_asc": False,
        # "file_name": "",  # 移除file_name参数，避免空值错误
        "include_metrics": ["convert_cnt", "conversion_cost", "conversion_rate"],
        "metrics_start_time": f"{start_date} 00:00:00",
        "metrics_end_time": f"{end_date} 00:00:00",
        "page": page,
        "limit": limit,
        "owner": {
            "owner_id": group_id,
            "owner_type": 9
        }
    }

    try:
        # 创建session以保持连接
        session = requests.Session()

        # 如果提供了cookies，添加到session中
        if cookies:
            session.cookies.update(cookies)

        # 发送POST请求
        response = session.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()

        # 解析响应
        try:
            result = response.json()
        except json.JSONDecodeError as e:
            print(f"JSON解析失败，响应内容前200字符: {response.text[:200]}...")
            print(f"响应编码: {response.encoding}")
            print(f"Content-Type: {response.headers.get('Content-Type')}")
            raise e

        if result.get("code") == 0:
            return {
                "videos": result.get("data", {}).get("videos", []),
                "pagination": result.get("data", {}).get("pagination", {}),
                "success": True
            }
        else:
            print(f"API返回错误: {result}")
            return {"success": False, "error": result}

    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return {"success": False, "error": str(e)}
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return {"success": False, "error": str(e)}
    except Exception as e:
        print(f"未知错误: {e}")
        return {"success": False, "error": str(e)}


# 使用示例
if __name__ == "__main__":
    # 示例参数（需要从浏览器获取最新的值）
    args_list=[{'GROUP_ID':"1807160717110459",'CSRF_TOKEN':"iqyKE9C6YRt3raqo0F_ThoAo","sessionid": "1b9a22c0421bd13ec0b5944520a14432","uid_tt": "d9e661fca3791b4e4749c271282c6b8d","sid_tt": "1b9a22c0421bd13ec0b5944520a14432"},
               {'GROUP_ID':"1783500144742474",'CSRF_TOKEN':"lTTNtX-UFUKUoRsQGATqZv-_","sessionid": "adfac4da324dfcdbb4638255a9bdf3fe","uid_tt": "28cbeca822b3fe5ac619a211c39af4f7","sid_tt": "adfac4da324dfcdbb4638255a9bdf3fe"},
               ]

    MATERIAL_ID = "7531596491141726254"

    for args in args_list:
        GROUP_ID=args['GROUP_ID']
        CSRF_TOKEN=args['CSRF_TOKEN']
        X_CSRF_TOKEN=args.get("X_CSRF_TOKEN",'')
        cookies={
        "csrftoken": CSRF_TOKEN,
        "sessionid": args.get("sessionid",''),
        "uid_tt": args.get("uid_tt",''),
        "sid_tt": args.get("sid_tt",'')
    }
   
        # 获取特定视频的URL
        video_info = get_video_url(
            group_id=GROUP_ID,
            material_id=MATERIAL_ID,
            csrf_token=CSRF_TOKEN,
            x_csrf_token=X_CSRF_TOKEN,
            cookies=cookies
        )

        if video_info:
            print("视频信息获取成功:")
            print(f"视频ID: {video_info['video_id']}")
            print(f"视频名称: {video_info['video_name']}")
            print(f"视频URL: {video_info['video_url']}")
            print(f"视频封面: {video_info['video_poster']}")
            print(f"创建时间: {video_info['create_time']}")
            print(f"时长: {video_info['duration']}秒")
            print(f"尺寸: {video_info['width']}x{video_info['height']}")
            print(f"指标: {video_info['metrics']}")
            break

        else:
            print("获取视频信息失败")
            continue

    print("\n" + "="*50 + "\n")

    # 获取多个视频
    # videos_result = get_multiple_videos(
    #     group_id=GROUP_ID,
    #     csrf_token=CSRF_TOKEN,
    #     x_csrf_token=X_CSRF_TOKEN,
    #     cookies=cookies,
    #     limit=5
    # )

    # if videos_result["success"]:
    #     print("视频列表获取成功:")
    #     print(f"总数: {videos_result['pagination']['total_count']}")
    #     print(f"当前页: {videos_result['pagination']['page']}")

    #     for i, video in enumerate(videos_result["videos"], 1):
    #         print(f"\n视频 {i}:")
    #         print(f"  名称: {video['video_name']}")
    #         print(f"  URL: {video['video_url']}")
    #         print(f"  创建时间: {video['create_time']}")
    # else:
    #     print("获取视频列表失败:", videos_result["error"])

