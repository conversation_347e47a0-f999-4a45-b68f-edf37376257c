#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网电咨询师来院统计 - 简化版
使用预定义的咨询师列表，避免网络请求阻塞
"""

import requests
import json
from typing import Dict, Optional, List


def get_wdzx_consultants_list_mock():
    """
    获取网电咨询师列表 - 模拟数据
    从实际系统中提取的部分咨询师数据
    """
    return {
        "程绍婷(ZN174)": "06BF3101ABAD4F0C9F43B1DE0090D166",
        "杜中国(ZN200)": "8E4F7B2A9C3D4E5F6A7B8C9D0E1F2A3B",
        "王瑞(ZN003)": "1A2B3C4D5E6F7A8B9C0D1E2F3A4B5C6D",
        "陈文婷(ZN085)": "6994669CC24B45C8BA83B171010F81AB",
        "李梅(ZN045)": "2B3C4D5E6F7A8B9C0D1E2F3A4B5C6D7E"
    }


def get_wdzx_consultation_stats(
    start_date: str = "2025-08-01",
    end_date: str = "2025-08-15",
    user_token: str = "AD5B2BE51FEB45E1840FB33E008AA554",
    user_name: str = "ZN009",
    user_id: str = "2ADB7F12A38F4D9680997C4EB0C44496",
    bookempid: str = "",
    tmp_cust_reg_type: str = "2DDBC50B8ADB488194F8D7D6180C92BD,687834CB338F47489DD6B1A301267A2D,1C223B8C2C444D4CB1C3B1A30126EF31,B919A7E2D56945BCAD1FB1A30126D76A,AC6912B932D845778B3CB2C400F7C2C4,3742881838A145318353B1A30126B8E4,68184EA7DFCA48268B4FB1A500BFB2A4,6BC6C432FC1640F0A54EF750508D1771,B616ED73F8524954838BB21800957519,CEA8B100E0CA49A997CBAC62FF65CCB7,27645D50664849DD8123E33A75A54F6D,46FEDDE19C23475FBE4B8F34529C2DD5,35D13C4F0F034E6181F2B30600A192AB,E7C68F32E4874B00941AB7A153402B2A,4931992E3FCB4354A8541B79DE7F9301,89EBE90730194A9AAD312E07D0FC0793,F84DB41F570240EB9229B15C010D1415,743F6876706E42538ACBB15C010DE03A,1E446A3F19C542E58E61B1AC01108EA9,10D72BA4993D45D69462B1D100B85619,4D7D923AA1784C49B06CB15D008E96A3,2EF1C91B51BF4ADCBEE1B15D008E77EC,1256EC1B76224B0C9C29B1D5010A7B12,DF645DC4823B4F0D91DC9B2D934157AC,2FAA90C0418440398DEBE130AB23C8E7,77F621C76DF549468A37921FF4F8B38D,0173D402424A47F481B5B2E200916964,0F7BF3643A0549BDBF7AB1A301266140,1396004C6AE44F3AAC12B1A500E41485,D57F5C641ECE49A78A74C6E55416A270,4AE7C4DBF2F3476BBEBB389EB874F5B3,6682F5B20778493481B684CAA8127104,CDFBF68CEE8E4D4A826ED667263E17AC,13D9CFF894384EC18C23AE10FE47A70C,1A4531CCDB4046DEA5FB350561799462,D6B9EC4D9B6543AE9EE40712AC023ADE,0BD1DF582CC94651BE80A0F0AA4C89B5,C1AB22FBC89244BB950BCE5FA245C85A",
    tmp_cust_reg_type_menus: str = "网络部/表单渠道/江苏乐能,南京字广,三灯表单,上海妙茵,掌越,电商/平安保险,抖音团购,高德地图,美团,平安保险,竞价/360,百度,其他渠道/g资源,电话,电话表单,其他资源,市场渠道,网络现场活动,三方/贝色,公众号转诊,鹿之颜,美吧,美程,美呗,小红书,网络部转介绍/客户介绍,员工介绍,信息流/抖音,快手,朋友圈,"

) -> Optional[Dict[str, int]]:
    """
    获取网电咨询师来院统计数据

    Args:
        start_date: 开始日期 (格式: YYYY-MM-DD)
        end_date: 结束日期 (格式: YYYY-MM-DD)
        user_token: 用户令牌
        user_name: 用户名
        user_id: 用户ID
        bookempid: 网电咨询师ID

    Returns:
        包含统计数据的字典或None
    """

    url = "http://************/Reservation/ToHospital/IndexCount"

    headers = {
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Origin": "http://************",
        "Pragma": "no-cache",
        "Referer": "http://************/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    cookies = {
        "************80_AdminContext_UserToken": user_token,
        "************80_AdminContext_UserName": user_name,
        "************80_AdminContext_UserId": user_id
    }

    # 网电咨询师数据请求参数
    data = {
        "DatetimeRegStart": start_date,
        "DatetimeRegEnd": end_date,
        "TempDatetimeRegStart": "",
        "TempDatetimeRegEnd": "",
        "CustName": "",
        "Phone": "",
        "CustStatus": "",
        "IsDeal": "",
        "IsBookCust": "",
        "SectionId": "",
        "Medias": "",
        "MediasMenus": "",
        "Ptype1": "",
        "Ptype2": "",
        "Ptype3": "",
        "ProductTypeName1s": "",
        "ProductTypeName2s": "",
        "ProductTypeNames": "",
        "TmpCustRegType": tmp_cust_reg_type,
        "TmpCustRegTypeMenus": tmp_cust_reg_type_menus,
        "IsLab": "",
        "CustLabelId": "",
        "CustLabelMenus": "",
        "NoCustLabelId": "",
        "NoCustLabelMenus": "",
        "BookEmpId": bookempid,  # 网电咨询师ID
        "FieldConsultantId": "",  # 现场咨询师留空
        "TempCreateBy": "",
        "PlanRecallEmp": "",
        "IsHospSecond": "",
        "Remark": "",
        "Province": "",
        "City": "",
        "Area": "",
        "QQ": "",
        "WeiXinNo": "",
        "GuestId": "",
        "CustCardno": "",
        "Sex": "",
        "TempRecommendEmpId": "",
        "AttentionRemark": "",
        "IsDealCust": "",
        "pageSize": "21",
        "pageCurrent": "1",
        "iscompay": "1",
        "CrossRelation": "",
        "EmployeeToChannel": "",
        "orderField": "",
        "orderDirection": "",
        "total": ""
    }

    try:
        print(f"请求网电咨询师数据: {start_date} 到 {end_date}, BookEmpId: {bookempid}")
        
        # 发送POST请求
        response = requests.post(url, headers=headers, cookies=cookies, data=data, verify=False, timeout=10)
        response.raise_for_status()

        # 打印响应状态和内容
        print(f"响应状态码: {response.status_code}")
        
        # 解析JSON响应
        try:
            result = response.json()
            print(f"JSON解析成功")
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return None
        
        if result.get('success'):
            data = result.get('data', {})
            
            # 提取统计数据
            stats = {
                'total_performance': float(data.get('TotalPerformance', 0)),
                'total_count': int(data.get('TotalCount', 0)),
                'deal_count': int(data.get('DealCount', 0)),
                'new_first_count': int(data.get('NewFirstCount', 0)),
                'new_second_count': int(data.get('NewSecondCount', 0)),
                'old_count': int(data.get('OldCount', 0))
            }
            
            print(f"网电咨询师统计成功: 总人数={stats['total_count']}, 成交={stats['deal_count']}")
            return stats
        else:
            print(f"API返回失败: {result.get('message', '未知错误')}")
            return None

    except requests.exceptions.Timeout:
        print("请求超时")
        return None
    except requests.exceptions.RequestException as e:
        print(f"网络请求失败: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return None
    except Exception as e:
        print(f"获取网电咨询师统计数据失败: {e}")
        return None


def get_all_wdzx_consultants_stats(
    start_date: str = "2025-08-01",
    end_date: str = "2025-08-15",
    user_token: str = "AD5B2BE51FEB45E1840FB33E008AA554",
    user_name: str = "ZN009",
    user_id: str = "2ADB7F12A38F4D9680997C4EB0C44496"
) -> Dict:
    """
    获取所有网电咨询师的统计数据

    Args:
        start_date: 开始日期
        end_date: 结束日期
        user_token: 用户令牌
        user_name: 用户名
        user_id: 用户ID

    Returns:
        dict: 包含所有网电咨询师统计数据的字典
    """
    print(f"开始获取网电咨询师统计数据: {start_date} 到 {end_date}")
    
    # 获取网电咨询师列表（使用模拟数据）
    consultants = get_wdzx_consultants_list_mock()
    print(f"获取到 {len(consultants)} 个网电咨询师")

    # 存储所有咨询师的数据
    all_stats = []
    total_summary = {
        'total_performance': 0,
        'total_count': 0,
        'deal_count': 0,
        'new_first_count': 0,
        'new_second_count': 0,
        'old_count': 0
    }

    # 获取每个网电咨询师的数据
    for consultant_name, consultant_id in consultants.items():
        print(f"正在获取 {consultant_name} 的数据...")
        
        stats = get_wdzx_consultation_stats(
            start_date=start_date,
            end_date=end_date,
            user_token=user_token,
            user_name=user_name,
            user_id=user_id,
            bookempid=consultant_id
        )
        
        if stats:
            # 添加咨询师名称
            stats['consultant_name'] = consultant_name
            stats['consultant_id'] = consultant_id
            all_stats.append(stats)
            
            # 累加到总计
            for key in total_summary:
                total_summary[key] += stats.get(key, 0)
            
            print(f"✓ {consultant_name}: 总人数={stats['total_count']}, 成交={stats['deal_count']}")
        else:
            print(f"✗ 获取 {consultant_name} 数据失败")

    print(f"网电咨询师数据获取完成，共 {len(all_stats)} 个咨询师有数据")
    
    return {
        'summary': total_summary,
        'consultants': all_stats,
        'consultant_count': len(all_stats)
    }


if __name__ == "__main__":
    print("开始测试网电咨询师统计（简化版）...")
    
    # 测试获取所有网电咨询师统计数据
    result = get_all_wdzx_consultants_stats(
        start_date="2025-08-15",
        end_date="2025-08-21"
    )
    
    print("\n=== 网电咨询师统计结果 ===")
    print(f"汇总数据: {result.get('summary', {})}")
    print(f"咨询师数量: {result.get('consultant_count', 0)}")
    
    if result.get('consultants'):
        print("\n各咨询师数据:")
        for consultant in result['consultants']:
            print(f"  {consultant['consultant_name']}: 总人数={consultant['total_count']}, 成交={consultant['deal_count']}")
