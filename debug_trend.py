#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import os
from datetime import datetime, timed<PERSON><PERSON>

def test_data_reading():
    """测试数据读取"""
    filename = '现场咨询统计_2025-08-01 到 2025-08-17.xlsx'
    
    print(f"测试文件: {filename}")
    print(f"文件存在: {os.path.exists(filename)}")
    
    if not os.path.exists(filename):
        print("文件不存在，退出测试")
        return
    
    try:
        # 读取Excel文件
        df = pd.read_excel(filename)
        print(f"文件读取成功，数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 查找杜中国
        du_rows = df[df['现场咨询师'].str.contains('杜中国', na=False)]
        print(f"杜中国相关行数: {len(du_rows)}")
        
        if len(du_rows) > 0:
            for _, row in du_rows.iterrows():
                print(f"咨询师: {row['现场咨询师']}")
                print(f"总人数: {row['总人数']}")
                print(f"成交人数: {row['成交人数']}")
                print(f"老客: {row['老客']}")
                print(f"新客首次: {row['新客首次']}")
                print(f"新客二次: {row['新客二次']}")
                print("---")
        else:
            print("没有找到杜中国的数据")
            print("所有咨询师:")
            for _, row in df.iterrows():
                if pd.notna(row['现场咨询师']) and row['现场咨询师'] != '汇总':
                    print(f"- {row['现场咨询师']}")
                    
    except Exception as e:
        print(f"读取文件时出错: {e}")
        import traceback
        traceback.print_exc()

def test_consultant_name_matching():
    """测试咨询师名称匹配"""
    filename = '现场咨询统计_2025-08-01 到 2025-08-17.xlsx'
    
    if not os.path.exists(filename):
        print("文件不存在")
        return
        
    try:
        df = pd.read_excel(filename)
        
        # 测试不同的匹配方式
        test_names = ['杜中国', '杜中国(ZN200)', 'ZN200']
        
        for test_name in test_names:
            print(f"\n测试匹配: '{test_name}'")
            
            # 精确匹配
            exact_match = df[df['现场咨询师'] == test_name]
            print(f"精确匹配结果: {len(exact_match)} 行")
            
            # 包含匹配
            contains_match = df[df['现场咨询师'].str.contains(test_name, na=False)]
            print(f"包含匹配结果: {len(contains_match)} 行")
            
            if len(contains_match) > 0:
                for _, row in contains_match.iterrows():
                    print(f"  找到: {row['现场咨询师']} - 总人数: {row['总人数']}")
                    
    except Exception as e:
        print(f"测试匹配时出错: {e}")

if __name__ == '__main__':
    print("=== 测试数据读取 ===")
    test_data_reading()
    
    print("\n=== 测试名称匹配 ===")
    test_consultant_name_matching()
