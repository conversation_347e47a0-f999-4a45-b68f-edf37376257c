import requests
import json
from datetime import datetime
import time
import pandas as pd

def get_tencent_ad_data(start_date, end_date):
    """
    获取腾讯广告账户数据
    
    参数:
    start_date (str): 开始日期，格式为 'YYYY-MM-DD'
    end_date (str): 结束日期，格式为 'YYYY-MM-DD'
    
    返回:
    dict: 包含总消费、曝光、点击和目标转化的字典
    """
    # 将日期转换为时间戳（毫秒）
    start_timestamp = int(time.mktime(datetime.strptime(start_date, '%Y-%m-%d').timetuple())) * 1000
    end_timestamp = int(time.mktime(datetime.strptime(end_date, '%Y-%m-%d').timetuple()) + 86399) * 1000  # 加上一天减1秒
    
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "content-type": "application/json; charset=UTF-8",
        "origin": "https://ad.qq.com",
        "priority": "u=1, i",
        "referer": "https://ad.qq.com/cm/home",
        "sec-ch-ua": "\"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0",
        "x-trace-id": "7c446da2-9153-3c83-3aeb-122f2e81d214"
    }
    
    cookies = {
        "pgv_pvi": "215352320",
        "pgv_pvid": "7184156880",
        "RK": "zzkVm3+K9Q",
        "ptcz": "2ebd43fddeedfc40d0e480ccb800aafcb03db337d800c998721e120d89914107",
        "_clck": "ggqmoc|1|fsp|0",
        "qq_domain_video_guid_verify": "0420327f036a3efa",
        "_qimei_uuid42": "19219102c16100571ebc3bce06f6cc6a59cae187eb",
        "_qimei_fingerprint": "15130563152950178be3c6e90e226d30",
        "_qimei_q36": "",
        "_qimei_h38": "8f95aa661ebc3bce06f6cc6a02000002b19219",
        "gdt_token": "TGT-75790-FOJcYVBhGhRnU9S7U42qIXMJJjbVq2yN2wgErufOwOrBoA1KHjOfgWXv8dRndUz3",
        "gdt_protect": "76158806f079d14956c4d60715856e8e6ce6601c"
    }
    
    url = "https://ad.qq.com/tap/v1/account_daily_report/account_list"
    
    params = {
        "g_tk": "*********",
        "trace_id": "7c446da2-9153-3c83-3aeb-122f2e81d214",
        "g_trans_id": "bc11e7e6-d825-3b68-c106-0f02d37bcce3",
        "unicode": "1",
        "request_source": "1",
        "is_user_dimension": "1"
    }
    
    payload = {
        "account_source": "GDT_PLATFROM",
        "filter_empty_data": 1,
        "data_version": "VERSION_ALL",
        "page": 1,
        "page_size": 20,
        "start_date_millons": start_timestamp,
        "end_date_millons": end_timestamp,
        "time_line": "REPORT_TIME",
        "new_source": 1,
        "dynamic_field_list": [
            "account_alias", "comment", "balance", "cost", "conversions_cost",
            "view_count", "valid_click_count", "ctr", "cpc", "conversions_count", "conversions_rate"
        ],
        "columnConfigId": 0,
        "user_id": ********
    }
    
    try:
        response = requests.post(url, headers=headers, cookies=cookies, params=params, json=payload)
        response.raise_for_status()  # 检查请求是否成功
        
        # 解析响应数据
        data = response.json()
        
        # 可选：将结果保存到文件
        with open('tencent_ad_data.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        
        # 提取总的数据
        if data.get('code') == 0 and 'data' in data:
            all_data = data['data'].get('all_account_sum_daily_report', {})
            
            # 提取所需的指标并处理格式
            total_cost = all_data.get('cost', '0').replace(',', '') if isinstance(all_data.get('cost', '0'), str) else '0'
            total_view = all_data.get('view_count', '0').replace(',', '') if isinstance(all_data.get('view_count', '0'), str) else '0'
            total_click = all_data.get('valid_click_count', '0').replace(',', '') if isinstance(all_data.get('valid_click_count', '0'), str) else '0'
            total_conversion = all_data.get('conversions_count', '0').replace(',', '') if isinstance(all_data.get('conversions_count', '0'), str) else '0'
            
            # 创建结果字典
            result = {
                '总消费': float(total_cost),
                '曝光量': int(total_view) if total_view.isdigit() else 0,
                '点击量': int(total_click) if total_click.isdigit() else 0,
                '目标转化': int(total_conversion) if total_conversion.isdigit() else 0
            }
            
            # 创建账户数据列表
            account_data = []
            for account in data['data'].get('list', []):
                daily_report = account.get('daily_report', {})
                
                # 处理数据格式
                cost = daily_report.get('cost', '0').replace(',', '') if isinstance(daily_report.get('cost', '0'), str) else '0'
                view = daily_report.get('view_count', '0').replace(',', '') if isinstance(daily_report.get('view_count', '0'), str) else '0'
                click = daily_report.get('valid_click_count', '0').replace(',', '') if isinstance(daily_report.get('valid_click_count', '0'), str) else '0'
                conversion = daily_report.get('conversions_count', '0').replace(',', '') if isinstance(daily_report.get('conversions_count', '0'), str) else '0'
                
                account_info = {
                    '账户ID': account.get('account_id', ''),
                    '账户名称': account.get('comment', ''),
                    '消费': float(cost),
                    '曝光量': int(view) if view.isdigit() else 0,
                    '点击量': int(click) if click.isdigit() else 0,
                    '目标转化': int(conversion) if conversion.isdigit() else 0
                }
                account_data.append(account_info)
            
            # 添加账户数据到结果中
            result['账户数据'] = account_data
            
            # 创建DataFrame
            df = pd.DataFrame(account_data)
            result['DataFrame'] = df
            
            return result
        else:
            print(f"API返回错误: {data.get('message', '未知错误')}")
            return None
        
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None
    except json.JSONDecodeError:
        print(f"JSON解析失败，响应内容: {response.text}")
        return None
    except Exception as e:
        print(f"处理数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

# 示例使用
if __name__ == "__main__":
    # 获取2025年3月20日到2025年3月24日的数据
    result = get_tencent_ad_data('2025-03-29', '2025-04-03')
    
    if result:
        print(f"总消费: {result['总消费']}")
        print(f"曝光量: {result['曝光量']}")
        print(f"点击量: {result['点击量']}")
        print(f"目标转化: {result['目标转化']}")
        
        # 打印账户数据
        print("\n账户数据:")
        for account in result['账户数据']:
            print(f"账户: {account['账户名称']}, 消费: {account['消费']}, 曝光: {account['曝光量']}, 点击: {account['点击量']}, 转化: {account['目标转化']}")
        
        # 打印DataFrame
        print("\nDataFrame:")
        print(result['DataFrame'])
        
        # 保存为Excel文件
        result['DataFrame'].to_excel('tencent_ad_data.xlsx', index=False)
        print("数据已保存到 tencent_ad_data.xlsx")
