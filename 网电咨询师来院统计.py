#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
网电咨询师来院统计
基于网电咨询来院统计.py，专门用于网电咨询师数据统计
"""

import requests
import json
import pandas as pd
from typing import Dict, Optional, List
from get_wdzx import get_consultants_list


def get_wdzx_consultation_stats(
    start_date: str = "2025-08-01",
    end_date: str = "2025-08-15",
    user_token: str = "AD5B2BE51FEB45E1840FB33E008AA554",
    user_name: str = "ZN009",
    user_id: str = "2ADB7F12A38F4D9680997C4EB0C44496",
    bookempid: str = "",
    tmp_cust_reg_type: str = "2DDBC50B8ADB488194F8D7D6180C92BD,687834CB338F47489DD6B1A301267A2D,1C223B8C2C444D4CB1C3B1A30126EF31,B919A7E2D56945BCAD1FB1A30126D76A,AC6912B932D845778B3CB2C400F7C2C4,3742881838A145318353B1A30126B8E4,68184EA7DFCA48268B4FB1A500BFB2A4,6BC6C432FC1640F0A54EF750508D1771,B616ED73F8524954838BB21800957519,CEA8B100E0CA49A997CBAC62FF65CCB7,27645D50664849DD8123E33A75A54F6D,46FEDDE19C23475FBE4B8F34529C2DD5,35D13C4F0F034E6181F2B30600A192AB,E7C68F32E4874B00941AB7A153402B2A,4931992E3FCB4354A8541B79DE7F9301,89EBE90730194A9AAD312E07D0FC0793,F84DB41F570240EB9229B15C010D1415,743F6876706E42538ACBB15C010DE03A,1E446A3F19C542E58E61B1AC01108EA9,10D72BA4993D45D69462B1D100B85619,4D7D923AA1784C49B06CB15D008E96A3,2EF1C91B51BF4ADCBEE1B15D008E77EC,1256EC1B76224B0C9C29B1D5010A7B12,DF645DC4823B4F0D91DC9B2D934157AC,2FAA90C0418440398DEBE130AB23C8E7,77F621C76DF549468A37921FF4F8B38D,0173D402424A47F481B5B2E200916964,0F7BF3643A0549BDBF7AB1A301266140,1396004C6AE44F3AAC12B1A500E41485,D57F5C641ECE49A78A74C6E55416A270,4AE7C4DBF2F3476BBEBB389EB874F5B3,6682F5B20778493481B684CAA8127104,CDFBF68CEE8E4D4A826ED667263E17AC,13D9CFF894384EC18C23AE10FE47A70C,1A4531CCDB4046DEA5FB350561799462,D6B9EC4D9B6543AE9EE40712AC023ADE,0BD1DF582CC94651BE80A0F0AA4C89B5,C1AB22FBC89244BB950BCE5FA245C85A",
    tmp_cust_reg_type_menus: str = "网络部/表单渠道/江苏乐能,南京字广,三灯表单,上海妙茵,掌越,电商/平安保险,抖音团购,高德地图,美团,平安保险,竞价/360,百度,其他渠道/g资源,电话,电话表单,其他资源,市场渠道,网络现场活动,三方/贝色,公众号转诊,鹿之颜,美吧,美程,美呗,小红书,网络部转介绍/客户介绍,员工介绍,信息流/抖音,快手,朋友圈,"

) -> Optional[Dict[str, int]]:
    """
    获取网电咨询师来院统计数据

    Args:
        start_date: 开始日期 (格式: YYYY-MM-DD)
        end_date: 结束日期 (格式: YYYY-MM-DD)
        user_token: 用户令牌
        user_name: 用户名
        user_id: 用户ID
        bookempid: 网电咨询师ID

    Returns:
        包含统计数据的字典:
        {
            'total_performance': 总开单业绩,
            'total_count': 总人数,
            'deal_count': 成交人数,
            'new_first_count': 新客首次,
            'new_second_count': 新客二次,
            'old_count': 老客
        }
        如果请求失败返回 None
    """

    url = "http://************/Reservation/ToHospital/IndexCount"

    headers = {
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Origin": "http://************",
        "Pragma": "no-cache",
        "Referer": "http://************/",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "X-Requested-With": "XMLHttpRequest"
    }

    cookies = {
        "************80_AdminContext_UserToken": user_token,
        "************80_AdminContext_UserName": user_name,
        "************80_AdminContext_UserId": user_id
    }

    # 网电咨询师数据请求参数
    data = {
        "DatetimeRegStart": start_date,
        "DatetimeRegEnd": end_date,
        "TempDatetimeRegStart": "",
        "TempDatetimeRegEnd": "",
        "CustName": "",
        "Phone": "",
        "CustStatus": "",
        "IsDeal": "",
        "IsBookCust": "",
        "SectionId": "",
        "Medias": "",
        "MediasMenus": "",
        "Ptype1": "",
        "Ptype2": "",
        "Ptype3": "",
        "ProductTypeName1s": "",
        "ProductTypeName2s": "",
        "ProductTypeNames": "",
        "TmpCustRegType": tmp_cust_reg_type,
        "TmpCustRegTypeMenus": tmp_cust_reg_type_menus,
        "IsLab": "",
        "CustLabelId": "",
        "CustLabelMenus": "",
        "NoCustLabelId": "",
        "NoCustLabelMenus": "",
        "BookEmpId": bookempid,  # 网电咨询师ID
        "FieldConsultantId": "",  # 网电咨询师留空
        "TempCreateBy": "",
        "PlanRecallEmp": "",
        "IsHospSecond": "",
        "Remark": "",
        "Province": "",
        "City": "",
        "Area": "",
        "QQ": "",
        "WeiXinNo": "",
        "GuestId": "",
        "CustCardno": "",
        "Sex": "",
        "TempRecommendEmpId": "",
        "AttentionRemark": "",
        "IsDealCust": "",
        "pageSize": "21",
        "pageCurrent": "1",
        "iscompay": "1",
        "CrossRelation": "",
        "EmployeeToChannel": "",
        "orderField": "",
        "orderDirection": "",
        "total": ""
    }

    try:
        response = requests.post(
            url,
            headers=headers,
            cookies=cookies,
            data=data,
            verify=False  # 对应curl的--insecure参数
        )

        if response.status_code == 200:
            result = response.json()

            if result.get("resStatus") == "1" and result.get("resCode") == "200":
                res_body = result.get("resBody", [])

                # 将数组转换为字典便于查找
                data_dict = {}
                for item in res_body:
                    data_dict[item.get("name")] = int(item.get("value", 0))


                # 计算一些有用的比率
                if data_dict['count'] > 0:
                    deal_rate = data_dict['dealcount'] / data_dict['count'] * 100
                    data_dict['deal_rate'] = f"{deal_rate:.2f}%"
                    
                    print(f"成交率: {data_dict['deal_rate']}")

                if data_dict['dealcount'] > 0:
                    avg_performance = data_dict['sumbill'] / data_dict['dealcount']
                    data_dict['avg_performance'] = f"{avg_performance:,.2f}"

                return {
                    "total_performance": data_dict.get("sumbill", 0),        # 总开单业绩
                    "total_count": data_dict.get("count", 0),               # 总人数
                    "deal_count": data_dict.get("dealcount", 0),            # 成交人数
                    "new_first_count": data_dict.get("newfirstcount", 0),   # 新客首次
                    "new_second_count": data_dict.get("newsecondcount", 0), # 新客二次
                    "old_count": data_dict.get("oldcount", 0),            # 老客
                    "avg_performance": data_dict.get("avg_performance", 0),  # 平均客单价
                    "deal_rate": data_dict.get("deal_rate", 0),             # 成交率

                }
            else:
                print(f"API返回错误: {result.get('resMessage', '未知错误')}")
                return None
        else:
            print(f"HTTP请求失败: {response.status_code}")
            return None

    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"未知错误: {e}")
        return None

def get_all_wdzx_consultants_stats_to_excel(
    start_date: str = "2025-08-01",
    end_date: str = "2025-08-15",
    user_token: str = "AD5B2BE51FEB45E1840FB33E008AA554",
    user_name: str = "ZN009",
    user_id: str = "2ADB7F12A38F4D9680997C4EB0C44496"
) -> Dict:
    """
    获取所有网电咨询师的统计数据并保存为Excel文件

    Args:
        start_date: 开始日期 (格式: YYYY-MM-DD)
        end_date: 结束日期 (格式: YYYY-MM-DD)
        user_token: 用户令牌
        user_name: 用户名
        user_id: 用户ID

    Returns:
        Excel文件路径
    """

    # 所有网电咨询师信息
    consultants = {'刘莹（网络）(ZN082)': '51EB24F67C3B4039B188B16F010A3E27', '吴众霞（网络）(ZN169)': '482FCA52C96C41C08FDFB1D600A933D9', '张晶晶（市场）(张晶晶（市场）)': '992435AA11134BECB41EB1AF01292F6B', '张晶晶（网络）(ZN077)': '9981D8772DE1497DA259B167009F9577', '徐帆(ZN199)': 'DA605DEDB8814383AC2EB1F500AD9964', '朱丽娟（市场）(朱丽娟（市场）)': '1297896173C14255B9AFB1AF01287B4C', '朱丽娟（网络）(ZN049)': '76B3463D8EE44B518658910411AD5170', '李梅（网络）(ZN045)': '054CCE08EAA649D999CB2DA1FC42E494', '李玲(ZN349)': '0D6F77B358074F1DBDA3B315008EE897', '杨欣鑫(ZN347)': '3CE302994D7141C8897CB2FE00E768BD', '王丽情（市场）(王丽情（市场）)': '57E519F0B08E49D1BB08B1AF01289A54', '王丽情（网络）(ZN081)': 'AEE1FB2F881E4636A5BDB16F0109EE8D', '王晶（市场）(王晶（市场）)': '1E4C8D4112A7444E92F3B1AF012858E3', '王晶（网络）(ZN072)': 'DC0554FBCF6B49EF8972B16500A0C039', '石璐(ZN208)': '2CACCFD228844F60BEFBB1FD00B58221', '胡梦璐（网络）(ZN076)': '7EEE9C3F3B5D456CA2D9B16500A16CD5', '蔡园青（市场）(蔡园青（市场）)': '9BDC85D98BF243D7B85BB1A3010E25F3', '蔡园青（网络）(ZN046)': '23E9FEC6B31C461F9649393603ABE56F', '许燕婷（网络）(ZN148)': '63DE3BA561D74ADEBE41B1B800AD8552', '谢宁宇(ZN191)': '51DC4B7E86EB451888B3B1F200E9AF45', '高可(ZN229)': '3ED543D215684EFF89C9B20A00EFEA57', '黄文静(ZN228)': '947BFADC16054FF488FCB20A00EF61E0', '黄文静（客户组 ）(ZN363)': '06BF81235D9145528E56B34C008D1DA1', '黄瑞杰(ZN218)': '56A643A306F64861BF89B2040125D291'}

    # 存储所有数据
    all_data = []

    print(f"开始获取 {start_date} 到 {end_date} 的网电咨询统计数据...")

    for consultant_name, consultant_id in consultants.items():
        print(f"正在获取 {consultant_name} 的数据...")

        # 获取该咨询师的数据
        stats = get_wdzx_consultation_stats(
            start_date=start_date,
            end_date=end_date,
            user_token=user_token,
            user_name=user_name,
            user_id=user_id,
            bookempid=consultant_id
        )
        print(stats)

        if stats:

            # 添加到数据列表
            all_data.append({
                "网电咨询师": consultant_name,
                "总开单业绩": stats['total_performance'],
                "总人数": stats['total_count'],
                "成交人数": stats['deal_count'],
                "成交率(%)":stats['deal_rate'],

                "新客首次": stats['new_first_count'],
                "新客二次": stats['new_second_count'],
                "老客": stats['old_count'],
                "客单价": stats['avg_performance']
            })
        else:
            print(f"获取 {consultant_name} 数据失败")
            # 添加空数据行
            all_data.append({
                "网电咨询师": consultant_name,
                "总开单业绩": 0,
                "总人数": 0,
                "成交人数": 0,
                "成交率(%)": 0,
                "新客首次": 0,
                "新客二次": 0,
                "老客": 0,
                "客单价": 0
            })

    # 创建DataFrame
    df = pd.DataFrame(all_data)

    # 计算汇总行
    total_row = {
        "网电咨询师": "汇总",
        "总开单业绩": df['总开单业绩'].sum(),
        "总人数": df['总人数'].sum(),
        "成交人数": df['成交人数'].sum(),
        "成交率(%)": f"{round((df['成交人数'].sum() / df['总人数'].sum() * 100) if df['总人数'].sum() > 0 else 0, 2)}%",#格式化字符串 百分号后面保留两位小数
        "新客首次": df['新客首次'].sum(),
        "新客二次": df['新客二次'].sum(),
        "老客": df['老客'].sum(),
        "客单价": round((df['总开单业绩'].sum() / df['成交人数'].sum()) if df['成交人数'].sum() > 0 else 0, 2)
    }

    # 添加汇总行
    df = pd.concat([df, pd.DataFrame([total_row])], ignore_index=True)

    # 生成文件名
    filename = f"网电咨询统计_{start_date} 到 {end_date}.xlsx"

    # 保存为Excel文件
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='网电咨询统计', index=False)

        # 获取工作表对象进行格式化
        worksheet = writer.sheets['网电咨询统计']

        # 设置列宽
        column_widths = {
            'A': 25,  # 网电咨询师
            'B': 15,  # 总开单业绩
            'C': 10,  # 总人数
            'D': 10,  # 成交人数
            'E': 12,  # 成交率
            'F': 10,  # 新客首次
            'G': 10,  # 新客二次
            'H': 10,  # 老客
            'I': 12   # 客单价
        }

        for col, width in column_widths.items():
            worksheet.column_dimensions[col].width = width

    print(f"数据已保存到文件: {filename}")
    print(f"共获取 {len(consultants)} 位网电咨询师的数据")

    return filename


if __name__ == "__main__":
    # 设置日期范围
    start_time = "2025-09-01"
    end_time = "2025-09-05"

    # 获取所有网电咨询师的统计数据并保存为Excel
    filename = get_all_wdzx_consultants_stats_to_excel(
        start_date=start_time,
        end_date=end_time
    )

    print(f"\n✅ 所有网电咨询师统计数据已成功导出到: {filename}")
    print("📊 Excel文件包含以下列:")
    print("   - 网电咨询师")
    print("   - 总开单业绩")
    print("   - 总人数")
    print("   - 成交人数")
    print("   - 成交率(%)")
    print("   - 新客首次")
    print("   - 新客二次")
    print("   - 老客")
    print("   - 客单价")
