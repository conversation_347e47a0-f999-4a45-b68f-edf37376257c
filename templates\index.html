<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <script src="{{ url_for('static', filename='echarts.min.js') }}"></script>
    <style>
        .float-nav {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .float-btn {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: #1890ff;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .float-btn:hover {
            transform: translateX(-5px);
            background: #1890ff;
            color: white;
            box-shadow: 0 2px 15px rgba(24, 144, 255, 0.3);
        }

        .nav-icon {
            font-style: normal;
        }

        @media (max-width: 768px) {
            .float-nav {
                right: 10px;
            }

            .float-btn {
                width: 40px;
                height: 40px;
                font-size: 12px;
            }
        }

        /* 年份选择器样式 */
        .year-tabs {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .year-btn {
            padding: 10px 20px;
            border: 2px solid #1890ff;
            background: white;
            color: #1890ff;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .year-btn:hover {
            background: #e6f7ff;
        }

        .year-btn.active {
            background: #1890ff;
            color: white;
        }

        /* 月份组样式 */
        .month-group {
            display: none;
        }

        .month-group.active {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 年份选择器 -->
        <div class="year-tabs">
            {% for year in years_data.keys() %}
            <button class="year-btn {% if year == latest_year %}active{% endif %}" data-year="{{ year }}">
                {{ year }}年
            </button>
            {% endfor %}
        </div>

        <!-- 月份 tab -->
        <div class="month-tabs">
            {% for year, files in years_data.items() %}
            <div class="month-group {% if year == latest_year %}active{% endif %}" data-year="{{ year }}">
                {% for file in files %}
                <button class="tab-btn {% if year == latest_year and file == latest_file %}active{% endif %}" data-file="{{ file }}" data-year="{{ year }}">
                    {{ file.replace('.xlsx', '') }}
                </button>
                {% endfor %}
            </div>
            {% endfor %}
        </div>

        <!-- 渠道 tab -->
        <div class="channel-tabs">
            <button class="channel-btn active" data-channel="抖音">抖音</button>
            <button class="channel-btn" data-channel="朋友圈">朋友圈</button>
            <button class="channel-btn" data-channel="百度">百度</button>
            <button class="channel-btn" data-channel="快手">快手</button>
            <button class="channel-btn" data-channel="高德">高德</button>
            <button class="channel-btn" data-channel="美团">美团</button>
            <button class="channel-btn" data-channel="合计">合计</button>
        </div>

        <h1>数据分析</h1>
        
        <div class="data-cards">
            <div class="card">
                <h3>建档总数</h3>
                <p id="totalRecords">0</p>
            </div>
            <div class="card">
                <h3>新客到院总人数</h3>
                <p id="totalVisits">0</p>
            </div>
            <div class="card">
                <h3>新客成交总金额</h3>
                <p id="totalAmount">0</p>
            </div>
            <div class="card">
                <h3>渠道消费</h3>
                <p id="channelCost">0</p>
            </div>
            <div class="card">
                <h3>投产比</h3>
                <p id="roi">0</p>
            </div>
        </div>

        <div class="charts">
            <div id="amountChart" class="chart"></div>
            <div id="visitChart" class="chart"></div>
            <div id="dealChart" class="chart"></div>
        </div>
        <div class="bar-charts">
            <div id="consultantChart" class="bar-chart"></div>
            <div id="consultantDealChart" class="bar-chart"></div>
            <div id="consultantAmountChart" class="bar-chart"></div>
        </div>
        <div class="line-charts">
            <div id="visitRateChart" class="line-chart"></div>
            <div id="dealRateChart" class="line-chart"></div>
            <div id="amountPerDealChart" class="line-chart"></div>
        </div>
        <div class="bar-charts">
            <div id="consultantRecordsChart" class="bar-chart"></div>
        </div>
    </div>
    <div class="float-nav">
        <a href="/feiyu" class="float-btn" title="抖音飞鱼数据分析">
            <i class="nav-icon">抖音</i>
        </a>
        <a href="/friend_circle" class="float-btn" title="朋友圈数据分析">
            <i class="nav-icon">朋友圈</i>
        </a>
        <a href="/wailian" class="float-btn" title="外链数据分析">
            <i class="nav-icon">外链</i>
        </a>
        <a href="/ks" class="float-btn" title="快手数据分析">
            <i class="nav-icon">快手</i>
        </a>
    </div>
    <script>
        window.yearsData = {{ years_data|tojson|safe }};
    </script>
    <script src="{{ url_for('static', filename='main.js') }}"></script>
</body>
</html> 