import requests

def get_bidding_promotion_list():
    url = "https://business.oceanengine.com/nbs/api/bm/promotion/ad/get_bidding_promotion_list"
    
    headers = {
        "sec-ch-ua-platform": "Windows",
        "x-csrftoken": "iqyKE9C6YRt3raqo0F_ThoAo",
        "x-csrf-token": "",
        "Referer": "https://business.oceanengine.com/site/account-manage/ad/bidding/superior/promotion",
        "sec-ch-ua": '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        "sec-ch-ua-mobile": "?0",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Content-Type": "application/json"
    }
    
    data = {
        "start_time": "**********",
        "end_time": "**********",
        "cascade_metrics": [
            "promotion_name", "promotion_id", "promotion_status_first", "promotion_status_second",
            "advertiser_name", "advertiser_id", "promotion_status_first_name", "promotion_status_second",
            "promotion_status_second_name", "promotion_status_first_name", "promotion_status_second",
            "promotion_status_second_name", "promotion_status_first_name", "promotion_status_second",
            "promotion_status_second_name"
        ],
        "fields": [
            "show_cnt", "cpm_platform", "stat_cost", "click_cnt", "ctr",
            "cpc_platform", "convert_cnt", "conversion_cost"
        ],
        "order_field": "create_time",
        "order_type": 1,
        "offset": 1,
        "limit": 100,
        "account_type": 0,
        "filter": {
            "advertiser": {},
            "group": {},
            "project": {
                "projectStatusFirst": [-1]
            },
            "promotion": {
                "promotionStatusFirst": [-1]
            }
        },
        "ocean_white": True
    }
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()
print(get_bidding_promotion_list())