from flask import Flask
import webbrowser
import threading
import time

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <html>
    <head><title>网电咨询师统计</title></head>
    <body style="font-family: Arial; margin: 40px;">
        <h1>🌐 网电咨询师来院数据统计系统</h1>
        <p>✅ 系统运行正常！</p>
        <p><a href="/dashboard" style="color: blue;">📊 查看可视化大屏</a></p>
    </body>
    </html>
    '''

@app.route('/dashboard')
def dashboard():
    return '''
    <html>
    <head><title>网电咨询师大屏</title></head>
    <body style="font-family: Arial; margin: 40px;">
        <h1>📊 网电咨询师可视化大屏</h1>
        <p>这里将显示网电咨询师的数据可视化大屏</p>
        <p>功能包括：</p>
        <ul>
            <li>总开单业绩统计</li>
            <li>客户类型分布</li>
            <li>成交率分析</li>
            <li>趋势图表</li>
        </ul>
        <p><a href="/" style="color: blue;">← 返回首页</a></p>
    </body>
    </html>
    '''

def open_browser():
    time.sleep(1)
    webbrowser.open('http://localhost:5007')

if __name__ == '__main__':
    print("启动网电咨询师统计应用...")
    print("访问地址: http://localhost:5007")
    
    # 在新线程中打开浏览器
    threading.Thread(target=open_browser, daemon=True).start()
    
    app.run(debug=False, host='0.0.0.0', port=5007)
