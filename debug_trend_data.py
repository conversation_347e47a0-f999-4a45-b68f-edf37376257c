#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
sys.path.append('.')

from consultant_stats_app import get_consultant_stats_data, generate_trend_data

def test_get_consultant_stats_data():
    """测试获取咨询师统计数据"""
    print("=== 测试 get_consultant_stats_data ===")
    
    data = get_consultant_stats_data('2025-08-01', '2025-08-17')
    
    if data:
        print("数据获取成功")
        print(f"咨询师数量: {len(data['consultants'])}")
        
        # 查找杜中国
        for consultant in data['consultants']:
            if '杜中国' in consultant['name']:
                print(f"找到杜中国: {consultant['name']}")
                print(f"  总人数: {consultant['total_people']}")
                print(f"  成交人数: {consultant['deal_people']}")
                print(f"  老客: {consultant['old_customer']}")
                print(f"  新客首次: {consultant['new_first']}")
                print(f"  新客二次: {consultant['new_second']}")
                print(f"  成交率: {consultant['deal_rate']}")
                print(f"  客单价: {consultant['avg_price']}")
                print(f"  总开单业绩: {consultant['total_performance']}")
                break
        else:
            print("没有找到杜中国")
            print("前5个咨询师:")
            for i, consultant in enumerate(data['consultants'][:5]):
                print(f"  {i+1}. {consultant['name']} - 总人数: {consultant['total_people']}")
    else:
        print("数据获取失败")

def test_generate_trend_data():
    """测试生成趋势数据"""
    print("\n=== 测试 generate_trend_data ===")
    
    # 测试指定咨询师
    trend_data = generate_trend_data('2025-08-01', '2025-08-17', 17, '杜中国(ZN200)')
    
    print("趋势数据结果:")
    print(f"日期: {trend_data.get('dates', [])}")
    print(f"总人数: {trend_data.get('total_people', [])}")
    print(f"老客: {trend_data.get('old_customer', [])}")
    print(f"新客首次: {trend_data.get('new_first', [])}")
    print(f"新客二次: {trend_data.get('new_second', [])}")
    print(f"成交率: {trend_data.get('deal_rate', [])}")
    print(f"客单价: {trend_data.get('avg_price', [])}")
    print(f"总开单业绩: {trend_data.get('total_performance', [])}")

if __name__ == '__main__':
    test_get_consultant_stats_data()
    test_generate_trend_data()
